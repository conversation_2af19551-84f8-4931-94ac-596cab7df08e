[ ] NAME:Multi-Agent System Demo Development DESCRIPTION:<PERSON><PERSON>p streamlined multi-agent system demo supporting dual modes and dual execution methods
-[ ] NAME:Core Agent Implementation DESCRIPTION:Implement core functionality for four agents, supporting dual modes (LLM/test) and dual execution (collaborative/standalone)
--[/] NAME:Summarization Agent Core DESCRIPTION:Implement threat classification: input raw logs, output predicted categories, calculate accuracy
--[ ] NAME:Prompt Agent Core DESCRIPTION:Implement description generation: input complete records, output high-quality Description text
--[ ] NAME:Comprehensive Decision Agent Core DESCRIPTION:Implement CVSS scoring: input threat descriptions, output Metrics and Base Score
--[ ] NAME:Specific Advice Agent Core DESCRIPTION:实现策略生成功能：输入instruction和input字段，输出策略列表（格式：['strategy1', 'strategy2', ...]），从预定义的策略选项中选择最合适的组合
-[ ] NAME:Collaborative Workflow Implementation DESCRIPTION:Implement end-to-end collaborative workflow via dedicated runner scripts
--[ ] NAME:Data Pipeline DESCRIPTION:Implement simple data passing and format conversion between agents
--[ ] NAME:Workflow Controller DESCRIPTION:Create workflow runner scripts for collaborative execution, agents run standalone directly
-[ ] NAME:Configuration System DESCRIPTION:Implement minimal configuration system supporting mode switching
--[ ] NAME:Dual Mode Configuration DESCRIPTION:Implement simple LLM mode and test mode switching mechanism
--[ ] NAME:Basic Parameter Management DESCRIPTION:Implement essential parameter management for necessary configuration options
-[ ] NAME:Demo Runner Scripts DESCRIPTION:Create collaborative workflow runner scripts, agents run standalone by direct execution