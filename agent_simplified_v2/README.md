# SAGIN Multi-Agent Threat Analysis System v2.0

## 概述

SAGIN (Space-Air-Ground Integrated Network) 多Agent威胁分析系统是一个专门为空天地一体化网络设计的智能安全分析平台。系统采用多Agent协作架构，能够自动分析网络威胁、生成安全建议并制定综合决策方案。

## 系统特性

### 🤖 四个核心Agent

1. **Summarization Agent** - 威胁数据摘要生成
   - 自动提取威胁关键信息
   - 生成结构化威胁摘要
   - 支持多种数据格式输入

2. **Prompt Agent** - 提示词生成和数据处理  
   - 智能提示词生成
   - 威胁数据标准化处理
   - 上下文信息增强

3. **Specific Advice Agent** - 特定建议生成
   - CVSS 3.1评分计算
   - 链式思维推理分析
   - 威胁特定安全建议

4. **Comprehensive Decision Agent** - 综合决策生成
   - 多Agent结果整合
   - 策略冲突解决
   - 最终决策和行动计划

### 🔧 核心功能

- **双运行模式**: 支持测试模式和LLM模式
- **协作工作流**: 4个Agent顺序协作处理
- **独立运行**: 每个Agent可单独执行
- **统一配置**: 集中化配置管理
- **模块化设计**: 高度解耦的核心模块
- **威胁分类**: 支持12种威胁类型识别
- **CVSS评分**: 自动化漏洞评分计算
- **文本分析**: 多种相似度计算方法

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd agent_simplified_v2

# 安装依赖（如需要）
pip install -r requirements.txt
```

### 2. 系统测试

```bash
# 运行系统测试
python test_system.py
```

### 3. 运行协作工作流

```bash
# 使用示例数据运行完整工作流
python main.py workflow --input data/input/sample_threat_data.json --output data/output
```

### 4. 运行独立Agent

```bash
# 运行单个Agent
python main.py agent summarization --input data/input/sample_threat_data.json --output result.json
```

## 使用指南

### 命令行接口

```bash
# 显示帮助信息
python main.py --help

# 列出所有可用Agent
python main.py list-agents

# 显示系统信息
python main.py info

# 运行协作工作流
python main.py workflow --input <input_file> --output <output_dir>

# 运行独立Agent
python main.py agent <agent_name> --input <input_file> --output <output_file>
```

### 支持的Agent名称

- `summarization` - 摘要生成Agent
- `prompt` - 提示词生成Agent  
- `specific_advice` - 特定建议Agent
- `comprehensive_decision` - 综合决策Agent

### 配置文件

系统使用YAML格式的配置文件，默认配置位于 `config/default_config.yaml`：

```yaml
# 模型配置
model:
  test_mode: true  # 测试模式开关
  llm_type: "local"  # LLM类型
  model_name: "llama3"
  temperature: 0.7

# 数据配置  
data:
  data_dir: "./data"
  input_dir: "./data/input"
  output_dir: "./data/output"

# Agent配置
agents:
  summarization:
    max_summary_length: 500
  prompt:
    max_prompts: 10
  specific_advice:
    chain_of_thought_steps: 5
  comprehensive_decision:
    decision_threshold: 0.5
```

## 项目结构

```
agent_simplified_v2/
├── core/                    # 核心共享模块
│   ├── __init__.py         # 模块导出
│   ├── config_manager.py   # 配置管理
│   ├── data_loader.py      # 数据加载
│   ├── threat_classifier.py # 威胁分类
│   ├── text_utils.py       # 文本处理
│   ├── base_agent.py       # Agent基类
│   └── logger_config.py    # 日志配置
├── agents/                  # Agent实现
│   ├── summarization_agent.py
│   ├── prompt_agent.py
│   ├── specific_advice_agent.py
│   └── comprehensive_decision_agent.py
├── config/                  # 配置文件
│   ├── __init__.py
│   └── default_config.yaml
├── data/                    # 数据目录
│   ├── input/              # 输入数据
│   └── output/             # 输出结果
├── main.py                 # 主运行脚本
├── test_system.py          # 系统测试脚本
└── README.md              # 项目文档
```

## 输入数据格式

系统支持JSON格式的威胁数据输入，示例格式：

```json
[
  {
    "id": "threat_001",
    "timestamp": "2024-01-15T10:30:00Z",
    "source": "SAGIN_MONITOR_01", 
    "threat_type": "DDoS",
    "severity": "HIGH",
    "description": "Detected distributed denial of service attack...",
    "affected_systems": ["SAT_COMM_01", "SAT_COMM_02"],
    "indicators": ["Abnormal traffic volume increase by 300%"],
    "impact_assessment": {
      "availability": 0.8,
      "confidentiality": 0.2,
      "integrity": 0.3
    },
    "metadata": {
      "detection_confidence": 0.95,
      "analyst_notes": "Coordinated attack pattern"
    }
  }
]
```

## 输出结果

### 协作工作流输出

运行协作工作流会在输出目录生成以下文件：

- `01_summarization_output.json` - 摘要生成结果
- `02_prompt_output.json` - 提示词生成结果  
- `03_specific_advice_output.json` - 特定建议结果
- `04_comprehensive_decision_output.json` - 综合决策结果
- `workflow_summary.json` - 工作流执行摘要

### 独立Agent输出

每个Agent会生成包含以下信息的JSON文件：

- Agent执行信息
- 处理结果数据
- 性能统计
- 置信度评分

## 支持的威胁类型

系统支持以下威胁类型的识别和分析：

1. **GPS_SPOOFING** - GPS欺骗攻击
2. **JAMMING** - 信号干扰攻击
3. **EAVESDROPPING** - 窃听攻击
4. **DENIAL_OF_SERVICE** - 拒绝服务攻击
5. **MALWARE** - 恶意软件攻击
6. **INSIDER_THREAT** - 内部威胁
7. **PHYSICAL_ATTACK** - 物理攻击
8. **DDoS** - 分布式拒绝服务攻击
9. **DoS** - 拒绝服务攻击
10. **Infiltration** - 渗透攻击
11. **Port Scan** - 端口扫描
12. **Web Attack** - Web攻击

## 开发指南

### 扩展新Agent

1. 继承 `BaseAgent` 类
2. 实现 `run()` 方法
3. 添加到主系统配置
4. 更新命令行接口

### 添加新威胁类型

1. 更新 `threat_classifier.py` 中的威胁映射
2. 修改配置文件中的威胁类型列表
3. 添加相应的处理逻辑

### 自定义配置

创建自定义配置文件并通过 `--config` 参数指定：

```bash
python main.py --config custom_config.yaml workflow --input data.json
```

## 故障排除

### 常见问题

1. **模块导入错误**: 确保Python路径正确设置
2. **配置文件错误**: 检查YAML格式和必需字段
3. **数据格式错误**: 验证输入JSON格式正确性
4. **权限问题**: 确保输出目录有写入权限

### 日志查看

系统会生成详细的日志文件 `sagin_agents.log`，包含：

- Agent执行过程
- 错误信息和堆栈跟踪
- 性能统计信息
- 调试信息

## 版本信息

- **当前版本**: 2.0.0
- **Python要求**: 3.7+
- **主要依赖**: 无外部依赖（核心功能）

## 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。

## 贡献指南

欢迎提交Issue和Pull Request来改进系统功能。

## 联系信息

如有问题或建议，请通过以下方式联系：

- 项目仓库: <repository-url>
- 问题反馈: <issues-url>
