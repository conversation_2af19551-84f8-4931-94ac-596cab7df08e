#!/usr/bin/env python3
"""
Prompt Agent - 描述格式转换Agent
负责将数组格式数据转换为CVE Description格式
"""

import sys
import re
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime

# 添加core模块到路径
sys.path.append(str(Path(__file__).parent.parent))

from core.base_agent import BaseAgent
from core.config_manager import Config
from core.data_loader import DataLoader
from core.logger_config import get_logger

logger = get_logger(__name__)


class PromptAgent(BaseAgent):
    """描述格式转换Agent - 将数组格式转换为CVE Description格式"""

    def __init__(self, config: Optional[Config] = None):
        super().__init__(config)

        # 初始化组件
        self.data_loader = DataLoader()

        logger.info("PromptAgent 初始化完成 - 描述格式转换器")

    def run(self, input_file: str, output_file: Optional[str] = None, input_data: Optional[Any] = None) -> Dict[str, Any]:
        """
        运行描述格式转换

        Args:
            input_file: 输入文件路径 (output_0515_dataset_fin.json) 或 None（如果使用input_data）
            output_file: 输出文件路径
            input_data: 直接输入的数据（来自SummarizationAgent）

        Returns:
            转换结果
        """
        logger.info("开始运行 PromptAgent 描述格式转换")

        try:
            # 加载数据
            if input_data is not None:
                # 使用传入的数据（协作工作流）
                data = input_data
                source = "collaboration_workflow"
            else:
                # 从文件加载数据（独立运行）
                data, _ = self.data_loader.load_threat_classification_data(input_file, remove_labels=False)
                source = input_file

            if not data:
                raise ValueError("无法加载输入数据")

            logger.info(f"加载数据成功: {len(data)} 条记录")

            # 执行格式转换
            converted_descriptions = self.convert_to_cve_format(data)

            # 构建结果
            result = {
                'agent': 'PromptAgent',
                'task': 'format_conversion',
                'source': source,
                'total_samples': len(data),
                'converted_descriptions': converted_descriptions,
                'processing_time': datetime.now().isoformat()
            }

            # 保存输出
            if output_file:
                self.save_output_data(result, output_file)
                result['output_file'] = output_file

            logger.info(f"描述格式转换完成，共转换 {len(converted_descriptions)} 条记录")
            return result

        except Exception as e:
            logger.error(f"PromptAgent 运行失败: {e}")
            raise

    def convert_to_cve_format(self, data: List[List]) -> List[Dict[str, str]]:
        """
        将数组格式数据转换为CVE Description格式

        Args:
            data: 数组格式的威胁数据列表

        Returns:
            CVE格式的描述列表
        """
        converted_descriptions = []

        for i, item in enumerate(data):
            if len(item) < 8:
                logger.warning(f"数据格式错误，跳过索引 {i}: {item}")
                continue

            try:
                # 解析数组数据
                _, layer, network_type, threat_type, timestamp, description, tag, _ = item

                # 生成CVE格式描述
                cve_description = self._generate_cve_description(
                    layer=layer,
                    network_type=network_type,
                    threat_type=threat_type,
                    timestamp=timestamp,
                    original_description=description,
                    tag=tag
                )

                converted_item = {
                    'CVE ID': f'CVE-2024-{i+1:05d}',  # 生成模拟CVE ID
                    'Time': timestamp,
                    'Description': cve_description,
                    'original_data': item
                }

                converted_descriptions.append(converted_item)

            except Exception as e:
                logger.warning(f"转换失败，跳过索引 {i}: {e}")
                continue

        return converted_descriptions

    def _generate_cve_description(self, layer: str, network_type: str, threat_type: str,
                                 timestamp: str, original_description: str, tag: str) -> str:
        """
        生成CVE格式的描述

        Args:
            layer: 网络层级
            network_type: 网络类型
            threat_type: 威胁类型
            timestamp: 时间戳
            original_description: 原始描述
            tag: 标签

        Returns:
            CVE格式的描述文本
        """
        # 构建CVE格式描述
        description_parts = []

        # 添加威胁概述
        description_parts.append(f"A {threat_type.lower()} vulnerability has been identified in the {layer} layer of {network_type} network infrastructure.")

        # 添加时间信息
        description_parts.append(f"The incident was detected at {timestamp}.")

        # 解析并添加技术细节
        technical_details = self._extract_technical_details(original_description)
        if technical_details:
            description_parts.append(f"Technical analysis reveals: {technical_details}")

        # 添加影响评估
        impact_assessment = self._generate_impact_assessment(threat_type, layer, network_type)
        description_parts.append(impact_assessment)

        # 添加标签信息
        if tag and tag != "Tag 1":
            description_parts.append(f"Classification: {tag}.")

        return " ".join(description_parts)

    def _extract_technical_details(self, original_description: str) -> str:
        """从原始描述中提取技术细节"""
        if not original_description:
            return ""

        details = []

        # 提取IP地址
        source_ip_match = re.search(r'Source IP:\s*\[([^\]]+)\]', original_description)
        dest_ip_match = re.search(r'Destination IP:\s*\[([^\]]+)\]', original_description)

        if source_ip_match:
            details.append(f"source IP {source_ip_match.group(1)}")
        if dest_ip_match:
            details.append(f"destination IP {dest_ip_match.group(1)}")

        # 提取端口信息
        port_match = re.search(r'Port:\s*\[([^\]]+)\]', original_description)
        if port_match:
            details.append(f"port {port_match.group(1)}")

        # 提取协议信息
        protocol_match = re.search(r'Protocol:\s*\[([^\]]+)\]', original_description)
        if protocol_match:
            details.append(f"protocol {protocol_match.group(1)}")

        return ", ".join(details) if details else "network traffic anomalies detected"

    def _generate_impact_assessment(self, threat_type: str, layer: str, network_type: str) -> str:
        """生成影响评估"""
        impact_templates = {
            'DDoS': f"This could lead to service disruption and network unavailability in the {layer} layer.",
            'GPS_spoofing': f"This may cause navigation errors and positioning inaccuracies in {network_type} systems.",
            'jamming': f"This could result in communication interference and signal degradation.",
            'eavesdropping': f"This poses risks of data interception and privacy breaches.",
            'malware': f"This could compromise system integrity and data security.",
            'insider_threat': f"This represents potential unauthorized access and data misuse.",
            'physical_attack': f"This could cause physical damage to {network_type} infrastructure."
        }

        return impact_templates.get(threat_type, f"This could impact the security and reliability of {network_type} network operations.")

    def validate_input_data(self, data: Any) -> bool:
        """验证输入数据格式"""
        if not isinstance(data, list):
            logger.error("输入数据必须是列表格式")
            return False

        if len(data) == 0:
            logger.error("输入数据列表为空")
            return False

        # 检查数组格式
        sample = data[0]
        if not isinstance(sample, list) or len(sample) < 8:
            logger.error("输入数据不符合预期的数组格式（至少8个元素）")
            return False

        logger.info("输入数据验证通过")
        return True

    def get_supported_input_format(self) -> str:
        """返回支持的输入数据格式说明"""
        return """
        支持的输入格式：
        - 文件：output_0515_dataset_fin.json（完整数据）
        - 格式：数组格式 [false, "ground", "Network Traffic", "DDoS", "时间戳", "描述", "Tag", 0]
        - 输出：CVE Description格式，适用于ComprehensiveDecisionAgent
        """


# 支持独立运行
if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='描述格式转换Agent')
    parser.add_argument('--input', required=True, help='输入文件路径')
    parser.add_argument('--output', help='输出文件路径')
    parser.add_argument('--config', help='配置文件路径')

    args = parser.parse_args()

    # 创建Agent并运行
    agent = PromptAgent()
    result = agent.run(args.input, args.output)

    print(f"描述格式转换完成！")
    print(f"转换样本数: {result['total_samples']}")
    if args.output:
        print(f"结果已保存到: {args.output}")
