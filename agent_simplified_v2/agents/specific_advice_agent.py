#!/usr/bin/env python3
"""
Specific Advice Agent - 策略建议生成器
负责生成策略建议数组，计算匹配率
"""

import sys
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

# 添加core模块到路径
sys.path.append(str(Path(__file__).parent.parent))

from core.base_agent import BaseAgent
from core.config_manager import Config
from core.data_loader import DataLoader
from core.evaluator import Evaluator
from core.logger_config import get_logger

logger = get_logger(__name__)


class SpecificAdviceAgent(BaseAgent):
    """策略建议生成器 - 生成策略建议数组，计算匹配率"""

    def __init__(self, config: Optional[Config] = None):
        super().__init__(config)

        # 初始化组件
        self.data_loader = DataLoader()
        self.evaluator = Evaluator()

        # 策略模板库
        self.strategy_templates = {
            'DDoS': [
                "实施DDoS防护系统",
                "配置流量限制和过滤",
                "部署CDN和负载均衡",
                "建立应急响应机制"
            ],
            'DoS': [
                "加强网络监控",
                "实施访问控制",
                "配置防火墙规则",
                "建立流量分析系统"
            ],
            'Infiltration': [
                "加强身份验证",
                "实施网络分段",
                "部署入侵检测系统",
                "定期安全审计"
            ],
            'Malware': [
                "部署反恶意软件",
                "实施文件完整性监控",
                "加强端点保护",
                "建立隔离机制"
            ],
            'Port Scan': [
                "配置端口扫描检测",
                "实施网络隐藏",
                "加强防火墙配置",
                "建立监控告警"
            ],
            'Web Attack': [
                "实施Web应用防火墙",
                "加强输入验证",
                "配置安全头部",
                "定期漏洞扫描"
            ]
        }

        logger.info("SpecificAdviceAgent 初始化完成 - 策略建议生成器")

    def run(self, input_file: str, output_file: Optional[str] = None, input_data: Optional[Any] = None) -> Dict[str, Any]:
        """
        运行策略建议生成

        Args:
            input_file: 输入文件路径
            output_file: 输出文件路径
            input_data: 来自ComprehensiveDecisionAgent的输入数据

        Returns:
            处理结果
        """
        logger.info(f"开始运行 SpecificAdviceAgent，输入文件: {input_file}")

        try:
            # 加载数据
            if input_data is not None:
                # 协作工作流模式
                data, labels = self._process_input_data(input_data)
                source = "collaboration_workflow"
            else:
                # 独立运行模式
                data, labels = self.data_loader.load_strategy_data_with_labels(input_file, remove_labels=True)
                source = input_file

            # 执行策略建议生成
            predictions = self.generate_strategy_recommendations(data)

            # 计算准确率
            accuracy_metrics = self.evaluator.calculate_strategy_matching_rate(predictions, labels)

            # 构建结果
            result = {
                'agent': 'SpecificAdviceAgent',
                'mode': 'test' if self.config.test_mode else 'llm',
                'source': source,
                'total_samples': len(data),
                'predictions': predictions,
                'accuracy_metrics': accuracy_metrics,
                'processing_time': datetime.now().isoformat()
            }

            # 保存输出
            if output_file:
                self.save_output_data(result, output_file)
                result['output_file'] = output_file

            logger.info(f"策略建议生成完成，匹配率: {accuracy_metrics['matching_rate']:.4f}")
            return result

        except Exception as e:
            logger.error(f"SpecificAdviceAgent 运行失败: {e}")
            raise

    def _process_input_data(self, input_data: Any) -> Tuple[List[Dict], List[List[str]]]:
        """
        处理来自ComprehensiveDecisionAgent的输入数据

        Args:
            input_data: ComprehensiveDecisionAgent的输出数据

        Returns:
            处理后的数据和标签
        """
        if isinstance(input_data, dict) and 'predictions' in input_data:
            # 从ComprehensiveDecisionAgent输出中提取CVSS预测
            predictions = input_data['predictions']
            data = []
            labels = []

            for item in predictions:
                data.append({
                    'CVE ID': item['CVE ID'],
                    'Base Score': item['Base Score'],
                    'Metrics': item['Metrics'],
                    'Description': item.get('original_description', '')
                })
                # 由于是协作工作流，没有真实标签，使用空标签
                labels.append([])

            return data, labels
        else:
            raise ValueError("输入数据格式不正确")

    def generate_strategy_recommendations(self, data: List[Dict]) -> List[List[str]]:
        """
        生成策略建议

        Args:
            data: 输入数据列表

        Returns:
            策略建议列表
        """
        recommendations = []

        for i, item in enumerate(data):
            try:
                # 提取信息
                description = item.get('Description', '')
                base_score = item.get('Base Score', 0.0)
                metrics = item.get('Metrics', [])

                # 基于描述和CVSS分数生成策略建议
                strategies = self._generate_strategies_for_item(description, base_score, metrics)

                recommendations.append(strategies)

            except Exception as e:
                logger.warning(f"策略生成失败，跳过索引 {i}: {e}")
                recommendations.append([])
                continue

        return recommendations

    def _generate_strategies_for_item(self, description: str, base_score: float, metrics: List[str]) -> List[str]:
        """
        为单个项目生成策略建议

        Args:
            description: 威胁描述
            base_score: CVSS基础分数
            metrics: CVSS指标

        Returns:
            策略建议列表
        """
        strategies = []

        # 基于描述识别威胁类型
        threat_type = self._identify_threat_type(description)

        # 获取基础策略模板
        base_strategies = self.strategy_templates.get(threat_type, [
            "实施综合安全监控",
            "加强访问控制",
            "定期安全评估",
            "建立应急响应机制"
        ])

        # 基于CVSS分数调整策略优先级
        if base_score >= 7.0:  # 高危
            strategies.extend([
                "立即实施紧急响应措施",
                "部署高级威胁检测系统"
            ])
        elif base_score >= 4.0:  # 中危
            strategies.extend([
                "加强监控和日志分析",
                "实施预防性安全措施"
            ])
        else:  # 低危
            strategies.extend([
                "定期安全检查",
                "基础防护措施维护"
            ])

        # 添加基础策略
        strategies.extend(base_strategies[:3])  # 限制数量

        # 基于CVSS指标添加特定策略
        if len(metrics) >= 8:
            if metrics[0] == 'Network':  # Attack Vector
                strategies.append("加强网络边界防护")
            if metrics[5] == 'High' or metrics[6] == 'High' or metrics[7] == 'High':  # CIA Impact
                strategies.append("实施数据保护措施")

        return strategies[:5]  # 限制策略数量

    def _identify_threat_type(self, description: str) -> str:
        """
        识别威胁类型

        Args:
            description: 威胁描述

        Returns:
            威胁类型
        """
        desc_lower = description.lower()

        # 威胁类型关键词映射
        threat_keywords = {
            'DDoS': ['ddos', 'distributed denial', 'flood', 'amplification'],
            'DoS': ['dos', 'denial of service', 'unavailable', 'crash'],
            'Infiltration': ['infiltration', 'penetration', 'breach', 'unauthorized access'],
            'Malware': ['malware', 'virus', 'trojan', 'ransomware', 'worm'],
            'Port Scan': ['port scan', 'scanning', 'reconnaissance', 'probe'],
            'Web Attack': ['web attack', 'sql injection', 'xss', 'csrf', 'web vulnerability']
        }

        # 匹配威胁类型
        for threat_type, keywords in threat_keywords.items():
            if any(keyword in desc_lower for keyword in keywords):
                return threat_type

        # 默认返回通用威胁类型
        return 'General'

    def validate_input_data(self, data: Any) -> bool:
        """验证输入数据格式"""
        if not isinstance(data, list):
            logger.error("输入数据必须是列表格式")
            return False

        if len(data) == 0:
            logger.error("输入数据列表为空")
            return False

        # 检查数据格式
        sample = data[0]
        if not isinstance(sample, dict):
            logger.error("输入数据必须是字典格式")
            return False

        logger.info("输入数据验证通过")
        return True

    def get_supported_input_format(self) -> str:
        """返回支持的输入数据格式说明"""
        return """
        支持的输入格式：
        - 文件：output_1112_strategy_train_data.json（去除output数据）
        - 格式：策略数据格式，来自ComprehensiveDecisionAgent输出
        - 输出：策略建议数组，计算匹配率
        """


# 支持独立运行
if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='策略建议生成Agent')
    parser.add_argument('--input', required=True, help='输入文件路径')
    parser.add_argument('--output', help='输出文件路径')
    parser.add_argument('--config', help='配置文件路径')

    args = parser.parse_args()

    # 创建Agent并运行
    agent = SpecificAdviceAgent()
    result = agent.run(args.input, args.output)

    print(f"策略建议生成完成！")
    print(f"处理样本数: {result['total_samples']}")
    print(f"匹配率: {result['accuracy_metrics']['matching_rate']:.4f}")
    if args.output:
        print(f"结果已保存到: {args.output}")
