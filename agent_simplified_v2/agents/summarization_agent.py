#!/usr/bin/env python3
"""
Summarization Agent - 威胁分类Agent
负责从威胁描述中分类威胁类型并计算准确率
"""

import sys
import os
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

# 添加core模块到路径
sys.path.append(str(Path(__file__).parent.parent))

from core.base_agent import BaseAgent
from core.config_manager import Config
from core.threat_classifier import ThreatClassifier
from core.text_utils import TextUtils
from core.data_loader import DataLoader
from core.evaluator import Evaluator
from core.logger_config import get_logger

logger = get_logger(__name__)


class SummarizationAgent(BaseAgent):
    """威胁分类Agent - 基于描述文本进行威胁分类并计算准确率"""

    def __init__(self, config: Optional[Config] = None):
        super().__init__(config)

        # 初始化组件
        self.data_loader = DataLoader()
        self.threat_classifier = ThreatClassifier()
        self.text_utils = TextUtils()
        self.evaluator = Evaluator()

        # Agent特定配置
        self.batch_size = getattr(self.config, 'batch_size', 10)
        self.max_threats = getattr(self.config, 'max_threats', 100)

        logger.info(f"SummarizationAgent 初始化完成 - 威胁分类器")
    
    def run(self, input_file: str, output_file: Optional[str] = None) -> Dict[str, Any]:
        """
        运行威胁分类

        Args:
            input_file: 输入文件路径 (output_0515_dataset_fin.json)
            output_file: 输出文件路径

        Returns:
            分类结果和准确率
        """
        logger.info(f"开始运行 SummarizationAgent 威胁分类，输入文件: {input_file}")

        try:
            # 加载威胁分类数据（去除标签用于预测）
            data, labels = self.data_loader.load_threat_classification_data(input_file, remove_labels=True)

            if not data or not labels:
                raise ValueError("无法加载威胁分类数据")

            logger.info(f"加载数据成功: {len(data)} 条记录")

            # 执行威胁分类
            predictions = self.classify_threats(data)

            # 计算准确率
            accuracy_metrics = self.evaluator.calculate_classification_accuracy(predictions, labels)

            # 构建结果
            result = {
                'agent': 'SummarizationAgent',
                'task': 'threat_classification',
                'input_file': input_file,
                'total_samples': len(data),
                'predictions': predictions,
                'ground_truth': labels,
                'accuracy_metrics': accuracy_metrics,
                'processing_time': datetime.now().isoformat(),
                'mode': 'test' if self.config.model.llm_type == 'test' else 'llm'
            }

            # 保存输出
            if output_file:
                self.save_output_data(result, output_file)
                result['output_file'] = output_file

            logger.info(f"威胁分类完成，准确率: {accuracy_metrics['accuracy']:.4f}")
            return result

        except Exception as e:
            logger.error(f"SummarizationAgent 运行失败: {e}")
            raise

    def classify_threats(self, data: List[List]) -> List[str]:
        """
        对威胁数据进行分类

        Args:
            data: 去除威胁类型标签的数组数据

        Returns:
            威胁类型预测列表
        """
        predictions = []

        for item in data:
            if len(item) < 7:  # 去除标签后应该有7个元素
                logger.warning(f"数据格式错误，跳过: {item}")
                predictions.append("UNKNOWN")
                continue

            # 提取描述文本（原索引5，去除标签后变为索引4）
            description = item[4] if len(item) > 4 else ""

            if not description:
                logger.warning("描述文本为空，使用默认分类")
                predictions.append("UNKNOWN")
                continue

            # 使用威胁分类器进行分类
            if self.config.model.llm_type == 'test':
                # 测试模式：基于关键词匹配
                classification_result = self.threat_classifier.classify_threat_from_description(description)
                predicted_type = classification_result['threat_type']
            else:
                # LLM模式：使用大模型进行分类
                predicted_type = self._classify_with_llm(description)

            predictions.append(predicted_type)

        logger.info(f"完成威胁分类，共处理 {len(predictions)} 条数据")
        return predictions

    def _classify_with_llm(self, description: str) -> str:
        """
        使用LLM进行威胁分类

        Args:
            description: 威胁描述文本

        Returns:
            预测的威胁类型
        """
        # TODO: 实现LLM分类逻辑
        # 这里可以调用OpenAI API或本地Llama模型

        # 暂时使用基于规则的分类作为fallback
        classification_result = self.threat_classifier.classify_threat_from_description(description)
        return classification_result['threat_type']
    
    def validate_input_data(self, data: Any) -> bool:
        """验证输入数据格式"""
        if not isinstance(data, list):
            logger.error("输入数据必须是列表格式")
            return False

        if len(data) == 0:
            logger.error("输入数据列表为空")
            return False

        # 检查数组格式
        sample = data[0]
        if not isinstance(sample, list) or len(sample) < 8:
            logger.error("输入数据不符合预期的数组格式（至少8个元素）")
            return False

        logger.info("输入数据验证通过")
        return True
    
    def get_supported_input_format(self) -> str:
        """返回支持的输入数据格式说明"""
        return """
        支持的输入格式：
        - 文件：output_0515_dataset_fin.json
        - 格式：数组格式 [false, "ground", "Network Traffic", "DDoS", "时间戳", "描述", "Tag", 0]
        - 说明：索引3为威胁类型（标签），索引5为描述文本（特征）
        """


# 支持独立运行
if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='威胁分类Agent')
    parser.add_argument('--input', required=True, help='输入文件路径')
    parser.add_argument('--output', help='输出文件路径')
    parser.add_argument('--config', help='配置文件路径')

    args = parser.parse_args()

    # 创建Agent并运行
    agent = SummarizationAgent()
    result = agent.run(args.input, args.output)

    print(f"威胁分类完成！")
    print(f"准确率: {result['accuracy_metrics']['accuracy']:.4f}")
    print(f"处理样本数: {result['total_samples']}")
    if args.output:
        print(f"结果已保存到: {args.output}")
