#!/usr/bin/env python3
"""
Comprehensive Decision Agent - CVSS评分Agent
负责预测CVSS指标和基础分数，计算准确率
"""

import sys
import json
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

# 添加core模块到路径
sys.path.append(str(Path(__file__).parent.parent))

from core.base_agent import BaseAgent
from core.config_manager import Config
from core.data_loader import DataLoader
from core.evaluator import Evaluator
from core.logger_config import get_logger

logger = get_logger(__name__)


class ComprehensiveDecisionAgent(BaseAgent):
    """CVSS评分Agent - 预测CVSS指标和基础分数"""

    def __init__(self, config: Optional[Config] = None):
        super().__init__(config)

        # 初始化组件
        self.data_loader = DataLoader()
        self.evaluator = Evaluator()

        # CVSS 3.1 指标定义
        self.cvss_metrics = {
            'Attack Vector': ['Network', 'Adjacent', 'Local', 'Physical'],
            'Attack Complexity': ['Low', 'High'],
            'Privileges Required': ['None', 'Low', 'High'],
            'User Interaction': ['None', 'Required'],
            'Scope': ['Unchanged', 'Changed'],
            'Confidentiality': ['None', 'Low', 'High'],
            'Integrity': ['None', 'Low', 'High'],
            'Availability': ['None', 'Low', 'High']
        }

        logger.info("ComprehensiveDecisionAgent 初始化完成 - CVSS评分器")

    def run(self, input_file: str, output_file: Optional[str] = None, input_data: Optional[Any] = None) -> Dict[str, Any]:
        """
        运行CVSS评分预测

        Args:
            input_file: 输入文件路径 (output_0525_finetune_metrics.json) 或 None（如果使用input_data）
            output_file: 输出文件路径
            input_data: 直接输入的数据（来自PromptAgent）

        Returns:
            评分结果和准确率
        """
        logger.info("开始运行 ComprehensiveDecisionAgent CVSS评分")

        try:
            # 加载数据
            if input_data is not None:
                # 使用传入的数据（协作工作流）
                data, labels = self._process_input_data(input_data)
                source = "collaboration_workflow"
            else:
                # 从文件加载数据（独立运行）
                data, labels = self.data_loader.load_cvss_data_with_labels(input_file, remove_labels=True)
                source = input_file

            if not data:
                raise ValueError("无法加载输入数据")

            logger.info(f"加载数据成功: {len(data)} 条记录")

            # 执行CVSS评分预测
            predictions = self.predict_cvss_scores(data)

            # 计算准确率
            accuracy_metrics = self.evaluator.calculate_cvss_score_error(predictions, labels)

            # 构建结果
            result = {
                'agent': 'ComprehensiveDecisionAgent',
                'task': 'cvss_scoring',
                'source': source,
                'total_samples': len(data),
                'predictions': predictions,
                'accuracy_metrics': accuracy_metrics,
                'processing_time': datetime.now().isoformat()
            }

            # 保存输出
            if output_file:
                self.save_output_data(result, output_file)
                result['output_file'] = output_file

            logger.info(f"CVSS评分完成，平均误差: {accuracy_metrics['mean_absolute_error']:.4f}")
            return result

        except Exception as e:
            logger.error(f"ComprehensiveDecisionAgent 运行失败: {e}")
            raise

    def _process_input_data(self, input_data: Any) -> Tuple[List[Dict], List[Dict]]:
        """
        处理来自PromptAgent的输入数据

        Args:
            input_data: PromptAgent的输出数据

        Returns:
            处理后的数据和标签
        """
        if isinstance(input_data, dict) and 'converted_descriptions' in input_data:
            # 从PromptAgent输出中提取描述
            descriptions = input_data['converted_descriptions']
            data = []
            labels = []

            for item in descriptions:
                data.append({
                    'CVE ID': item['CVE ID'],
                    'Time': item['Time'],
                    'Description': item['Description']
                })
                # 由于是协作工作流，没有真实标签，使用空标签
                labels.append({})

            return data, labels
        else:
            raise ValueError("输入数据格式不正确")

    def predict_cvss_scores(self, data: List[Dict]) -> List[Dict[str, Any]]:
        """
        预测CVSS指标和基础分数

        Args:
            data: 输入数据列表

        Returns:
            CVSS预测结果列表
        """
        predictions = []

        for i, item in enumerate(data):
            try:
                description = item.get('Description', '')
                cve_id = item.get('CVE ID', f'CVE-{i+1:05d}')

                # 基于描述预测CVSS指标
                metrics_prediction = self._predict_cvss_metrics(description)

                # 计算基础分数
                base_score = self._calculate_base_score(metrics_prediction)

                prediction = {
                    'CVE ID': cve_id,
                    'Metrics': metrics_prediction,
                    'Base Score': base_score,
                    'original_description': description
                }

                predictions.append(prediction)

            except Exception as e:
                logger.warning(f"预测失败，跳过索引 {i}: {e}")
                continue

        return predictions

    def _predict_cvss_metrics(self, description: str) -> List[str]:
        """
        基于描述预测CVSS指标

        Args:
            description: 威胁描述

        Returns:
            CVSS指标预测结果
        """
        # 基于关键词的简单预测逻辑（测试模式）
        metrics = []

        # Attack Vector
        if any(word in description.lower() for word in ['network', 'remote', 'internet']):
            metrics.append('Network')
        elif any(word in description.lower() for word in ['adjacent', 'local network']):
            metrics.append('Adjacent')
        elif any(word in description.lower() for word in ['local', 'system']):
            metrics.append('Local')
        else:
            metrics.append('Physical')

        # Attack Complexity
        if any(word in description.lower() for word in ['simple', 'easy', 'basic']):
            metrics.append('Low')
        else:
            metrics.append('High')

        # Privileges Required
        if any(word in description.lower() for word in ['admin', 'root', 'privileged']):
            metrics.append('High')
        elif any(word in description.lower() for word in ['user', 'authenticated']):
            metrics.append('Low')
        else:
            metrics.append('None')

        # User Interaction
        if any(word in description.lower() for word in ['click', 'user', 'interaction']):
            metrics.append('Required')
        else:
            metrics.append('None')

        # Scope
        if any(word in description.lower() for word in ['system', 'multiple', 'spread']):
            metrics.append('Changed')
        else:
            metrics.append('Unchanged')

        # Confidentiality Impact
        if any(word in description.lower() for word in ['data', 'information', 'leak']):
            metrics.append('High')
        elif any(word in description.lower() for word in ['partial', 'limited']):
            metrics.append('Low')
        else:
            metrics.append('None')

        # Integrity Impact
        if any(word in description.lower() for word in ['modify', 'alter', 'corrupt']):
            metrics.append('High')
        elif any(word in description.lower() for word in ['partial', 'limited']):
            metrics.append('Low')
        else:
            metrics.append('None')

        # Availability Impact
        if any(word in description.lower() for word in ['dos', 'denial', 'unavailable', 'crash']):
            metrics.append('High')
        elif any(word in description.lower() for word in ['slow', 'degraded']):
            metrics.append('Low')
        else:
            metrics.append('None')

        return metrics

    def _calculate_base_score(self, metrics: List[str]) -> float:
        """
        计算CVSS基础分数

        Args:
            metrics: CVSS指标列表

        Returns:
            基础分数 (0.0-10.0)
        """
        if len(metrics) != 8:
            logger.warning(f"CVSS指标数量不正确: {len(metrics)}, 期望8个")
            return 5.0  # 默认中等分数

        # CVSS 3.1 分数映射
        av_scores = {'Network': 0.85, 'Adjacent': 0.62, 'Local': 0.55, 'Physical': 0.2}
        ac_scores = {'Low': 0.77, 'High': 0.44}
        pr_scores = {'None': 0.85, 'Low': 0.62, 'High': 0.27}
        ui_scores = {'None': 0.85, 'Required': 0.62}
        s_scores = {'Unchanged': 1.0, 'Changed': 1.08}
        cia_scores = {'None': 0.0, 'Low': 0.22, 'High': 0.56}

        try:
            av = av_scores.get(metrics[0], 0.85)
            ac = ac_scores.get(metrics[1], 0.77)
            pr = pr_scores.get(metrics[2], 0.85)
            ui = ui_scores.get(metrics[3], 0.85)
            s = s_scores.get(metrics[4], 1.0)
            c = cia_scores.get(metrics[5], 0.0)
            i = cia_scores.get(metrics[6], 0.0)
            a = cia_scores.get(metrics[7], 0.0)

            # 计算影响子分数
            iss = 1 - ((1 - c) * (1 - i) * (1 - a))

            # 计算可利用性子分数
            exploitability = 8.22 * av * ac * pr * ui

            # 计算基础分数
            if iss <= 0:
                base_score = 0.0
            elif s == 1.0:  # Scope Unchanged
                impact = 6.42 * iss
                base_score = min(10.0, (impact + exploitability))
            else:  # Scope Changed
                impact = 7.52 * (iss - 0.029) - 3.25 * pow(iss - 0.02, 15)
                base_score = min(10.0, 1.08 * (impact + exploitability))

            # 四舍五入到一位小数
            return round(max(0.0, base_score), 1)

        except Exception as e:
            logger.warning(f"CVSS分数计算失败: {e}")
            return 5.0

    def validate_input_data(self, data: Any) -> bool:
        """验证输入数据格式"""
        if not isinstance(data, list):
            logger.error("输入数据必须是列表格式")
            return False

        if len(data) == 0:
            logger.error("输入数据列表为空")
            return False

        # 检查数据格式
        sample = data[0]
        if not isinstance(sample, dict):
            logger.error("输入数据必须是字典格式")
            return False

        # 检查必需字段
        required_fields = ['CVE ID', 'Description']
        for field in required_fields:
            if field not in sample:
                logger.error(f"输入数据缺少必需字段: {field}")
                return False

        logger.info("输入数据验证通过")
        return True

    def get_supported_input_format(self) -> str:
        """返回支持的输入数据格式说明"""
        return """
        支持的输入格式：
        - 文件：output_0525_finetune_metrics.json（去除Metrics和Base Score）
        - 格式：CVE Description格式，来自PromptAgent输出
        - 输出：CVSS指标预测和基础分数，计算准确率
        """


# 支持独立运行
if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='CVSS评分Agent')
    parser.add_argument('--input', required=True, help='输入文件路径')
    parser.add_argument('--output', help='输出文件路径')
    parser.add_argument('--config', help='配置文件路径')

    args = parser.parse_args()

    # 创建Agent并运行
    agent = ComprehensiveDecisionAgent()
    result = agent.run(args.input, args.output)

    print(f"CVSS评分完成！")
    print(f"处理样本数: {result['total_samples']}")
    print(f"平均误差: {result['accuracy_metrics']['mean_absolute_error']:.4f}")
    if args.output:
        print(f"结果已保存到: {args.output}")
