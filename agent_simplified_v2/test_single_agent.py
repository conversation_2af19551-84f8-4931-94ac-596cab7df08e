#!/usr/bin/env python3
"""
单个Agent测试脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from agents.summarization_agent import SummarizationAgent
from core import setup_logger

logger = setup_logger(__name__)

def test_summarization_agent():
    """测试SummarizationAgent"""
    print("🤖 测试SummarizationAgent...")
    
    try:
        agent = SummarizationAgent()
        input_file = "data/input/sample_threat_data.json"
        output_file = "data/output/test_summarization.json"
        
        # 确保输出目录存在
        Path("data/output").mkdir(parents=True, exist_ok=True)
        
        result = agent.run(input_file, output_file)
        
        print(f"✅ SummarizationAgent 测试成功")
        print(f"   处理的威胁数量: {result.get('total_threats', 0)}")
        print(f"   输出文件: {result.get('output_file', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ SummarizationAgent 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_summarization_agent()
    sys.exit(0 if success else 1)
