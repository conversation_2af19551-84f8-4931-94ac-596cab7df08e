#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Summarization Agent - Threat Classification
Standalone executable agent for threat classification task

Core functionality:
- Input: Raw logs from output_0515_dataset_fin.json (ignore category field)
- Output: Predicted attack category string
- Evaluation: Calculate classification accuracy
- Modes: Test mode (rule-based) and LLM mode
"""

import json
import os
import sys
import argparse
from typing import List, Dict, Any, Tuple
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ThreatClassifier:
    """Rule-based threat classifier for test mode"""
    
    def __init__(self):
        # Define threat classification rules based on keywords
        self.classification_rules = {
            'DDoS': ['ddos', 'distributed denial', 'loit', 'flood', 'overwhelm'],
            'DoS': ['dos', 'denial of service', 'slowloris', 'goldeneye', 'hulk', 'slowhttptest'],
            'Infiltration': ['infiltration', 'unauthorized access', 'privilege escalation', 'meta exploit'],
            'PortScan': ['port scan', 'nmap', 'scanning', 'reconnaissance'],
            'BruteForce': ['brute force', 'login attempt', 'password', 'ftp brute'],
            'Botnet': ['botnet', 'bot', 'command and control', 'c&c'],
            'Web Attack': ['web attack', 'sql injection', 'xss', 'http'],
        }
    
    def classify_threat(self, log_description: str) -> str:
        """
        Classify threat based on description content
        
        Args:
            log_description: Raw log description text
            
        Returns:
            Predicted threat category
        """
        if not log_description:
            return 'Unknown'
        
        description_lower = log_description.lower()
        
        # Check each category's keywords
        for category, keywords in self.classification_rules.items():
            for keyword in keywords:
                if keyword in description_lower:
                    return category
        
        return 'Unknown'


class LLMInterface:
    """Mock LLM interface for demonstration"""
    
    def __init__(self, model_path: str = None):
        self.model_path = model_path
        logger.info(f"LLM Interface initialized with model: {model_path}")
    
    def classify_threat(self, log_description: str) -> str:
        """
        Mock LLM classification - in real implementation, this would call actual LLM
        
        Args:
            log_description: Raw log description text
            
        Returns:
            Predicted threat category
        """
        # For demo purposes, use rule-based classification
        # In real implementation, this would call the LLM model
        classifier = ThreatClassifier()
        return classifier.classify_threat(log_description)


class SummarizationAgent:
    """Main Summarization Agent class"""
    
    def __init__(self, test_mode: bool = True, model_path: str = None):
        self.test_mode = test_mode
        self.model_path = model_path
        
        if test_mode:
            self.classifier = ThreatClassifier()
            logger.info("Initialized in test mode with rule-based classifier")
        else:
            self.classifier = LLMInterface(model_path)
            logger.info("Initialized in LLM mode")
    
    def load_data(self, file_path: str) -> List[Dict[str, Any]]:
        """
        Load threat data from JSON file
        
        Args:
            file_path: Path to the data file
            
        Returns:
            List of threat records
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Data file not found: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        logger.info(f"Loaded {len(data)} records from {file_path}")
        return data
    
    def extract_description(self, record: List) -> str:
        """
        Extract description from record format
        Based on the data format: [false, "ground", "Network Traffic", "DDoS", "2024-04-13 09:30:17", "description...", "Tag 1", 0]
        
        Args:
            record: Single record from the dataset
            
        Returns:
            Description text
        """
        if len(record) >= 6:
            return record[5]  # Description is at index 5
        return ""
    
    def get_true_category(self, record: List) -> str:
        """
        Extract true category from record (for evaluation)
        
        Args:
            record: Single record from the dataset
            
        Returns:
            True threat category
        """
        if len(record) >= 4:
            return record[3]  # Category is at index 3
        return "Unknown"
    
    def classify_single_threat(self, description: str) -> str:
        """
        Classify a single threat description
        
        Args:
            description: Threat description text
            
        Returns:
            Predicted category
        """
        return self.classifier.classify_threat(description)
    
    def process_dataset(self, file_path: str) -> Dict[str, Any]:
        """
        Process entire dataset and return results
        
        Args:
            file_path: Path to input data file
            
        Returns:
            Processing results with predictions and metrics
        """
        # Load data
        data = self.load_data(file_path)
        
        results = {
            'total_records': len(data),
            'predictions': [],
            'true_categories': [],
            'processing_time': datetime.now().isoformat(),
            'mode': 'test' if self.test_mode else 'llm'
        }
        
        # Process each record
        for i, record in enumerate(data):
            description = self.extract_description(record)
            true_category = self.get_true_category(record)
            predicted_category = self.classify_single_threat(description)
            
            results['predictions'].append(predicted_category)
            results['true_categories'].append(true_category)
            
            if (i + 1) % 100 == 0:
                logger.info(f"Processed {i + 1}/{len(data)} records")
        
        # Calculate accuracy
        accuracy = self.calculate_accuracy(results['true_categories'], results['predictions'])
        results['accuracy'] = accuracy
        
        logger.info(f"Processing completed. Accuracy: {accuracy:.2%}")
        return results
    
    def calculate_accuracy(self, true_labels: List[str], predictions: List[str]) -> float:
        """
        Calculate classification accuracy
        
        Args:
            true_labels: List of true categories
            predictions: List of predicted categories
            
        Returns:
            Accuracy score (0-1)
        """
        if len(true_labels) != len(predictions):
            raise ValueError("Length mismatch between true labels and predictions")
        
        correct = sum(1 for true, pred in zip(true_labels, predictions) if true == pred)
        return correct / len(true_labels) if true_labels else 0.0
    
    def save_results(self, results: Dict[str, Any], output_path: str = None) -> str:
        """
        Save results to JSON file
        
        Args:
            results: Processing results
            output_path: Output file path
            
        Returns:
            Path to saved file
        """
        if output_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"summarization_results_{timestamp}.json"
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Results saved to: {output_path}")
        return output_path


def main():
    """Main function for standalone execution"""
    parser = argparse.ArgumentParser(description='Summarization Agent - Threat Classification')
    parser.add_argument('--input', default='../output_0515_dataset_fin.json', 
                       help='Input data file path')
    parser.add_argument('--output', default=None, 
                       help='Output results file path')
    parser.add_argument('--test-mode', action='store_true', default=True,
                       help='Use test mode (rule-based classification)')
    parser.add_argument('--llm-mode', action='store_true', 
                       help='Use LLM mode')
    parser.add_argument('--model-path', default=None,
                       help='Path to LLM model (for LLM mode)')
    
    args = parser.parse_args()
    
    # Determine mode
    test_mode = not args.llm_mode if args.llm_mode else args.test_mode
    
    print("="*60)
    print("Summarization Agent - Threat Classification")
    print("="*60)
    print(f"Mode: {'Test (Rule-based)' if test_mode else 'LLM'}")
    print(f"Input file: {args.input}")
    print(f"Model path: {args.model_path if not test_mode else 'N/A'}")
    print()
    
    try:
        # Initialize agent
        agent = SummarizationAgent(test_mode=test_mode, model_path=args.model_path)
        
        # Process dataset
        results = agent.process_dataset(args.input)
        
        # Save results
        output_path = agent.save_results(results, args.output)
        
        # Display summary
        print("\n" + "="*60)
        print("PROCESSING SUMMARY")
        print("="*60)
        print(f"Total records processed: {results['total_records']}")
        print(f"Classification accuracy: {results['accuracy']:.2%}")
        print(f"Results saved to: {output_path}")
        print("="*60)
        
    except Exception as e:
        logger.error(f"Error during processing: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
