#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configuration System for Multi-Agent Demo
Simple configuration management supporting mode switching and parameter management
"""

import json
import os
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict


@dataclass
class AgentConfig:
    """Configuration for individual agents"""
    test_mode: bool = True
    model_path: Optional[str] = None
    max_records: Optional[int] = None
    show_samples: int = 3


@dataclass
class WorkflowConfig:
    """Configuration for collaborative workflow"""
    test_mode: bool = True
    model_path: Optional[str] = None
    max_records: Optional[int] = None
    save_results: bool = True
    intermediate_cleanup: bool = True


@dataclass
class DataConfig:
    """Configuration for data file paths"""
    raw_logs: str = "../../output_0515_dataset_fin.json"
    cvss_data: str = "../../output_0525_finetune_metrics.json"
    strategy_data: str = "../../output_1112_strategy_train_data.json"


@dataclass
class SystemConfig:
    """Complete system configuration"""
    agent: AgentConfig
    workflow: WorkflowConfig
    data: DataConfig
    
    def __post_init__(self):
        """Ensure agent and workflow modes are synchronized"""
        if self.agent.test_mode != self.workflow.test_mode:
            # Workflow mode takes precedence
            self.agent.test_mode = self.workflow.test_mode
        
        if self.agent.model_path != self.workflow.model_path:
            # Workflow model path takes precedence
            self.agent.model_path = self.workflow.model_path


class ConfigManager:
    """Configuration manager for the multi-agent system"""
    
    def __init__(self, config_file: str = "system_config.json"):
        self.config_file = config_file
        self.config = self.load_config()
    
    def load_config(self) -> SystemConfig:
        """
        Load configuration from file or create default
        
        Returns:
            System configuration
        """
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                return SystemConfig(
                    agent=AgentConfig(**config_data.get('agent', {})),
                    workflow=WorkflowConfig(**config_data.get('workflow', {})),
                    data=DataConfig(**config_data.get('data', {}))
                )
            except Exception as e:
                print(f"Warning: Failed to load config file {self.config_file}: {e}")
                print("Using default configuration")
        
        # Return default configuration
        return SystemConfig(
            agent=AgentConfig(),
            workflow=WorkflowConfig(),
            data=DataConfig()
        )
    
    def save_config(self) -> str:
        """
        Save current configuration to file
        
        Returns:
            Path to saved configuration file
        """
        config_dict = {
            'agent': asdict(self.config.agent),
            'workflow': asdict(self.config.workflow),
            'data': asdict(self.config.data)
        }
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, ensure_ascii=False, indent=2)
        
        return self.config_file
    
    def set_mode(self, test_mode: bool, model_path: Optional[str] = None):
        """
        Set operation mode for all components
        
        Args:
            test_mode: Whether to use test mode
            model_path: Path to LLM model (for LLM mode)
        """
        self.config.agent.test_mode = test_mode
        self.config.workflow.test_mode = test_mode
        
        if not test_mode and model_path:
            self.config.agent.model_path = model_path
            self.config.workflow.model_path = model_path
    
    def set_max_records(self, max_records: Optional[int]):
        """
        Set maximum records limit for all components
        
        Args:
            max_records: Maximum number of records to process
        """
        self.config.agent.max_records = max_records
        self.config.workflow.max_records = max_records
    
    def get_agent_args(self, agent_type: str = None) -> Dict[str, Any]:
        """
        Get command line arguments for agent execution
        
        Args:
            agent_type: Type of agent (for specific customizations)
            
        Returns:
            Dictionary of arguments
        """
        args = {
            'test_mode': self.config.agent.test_mode,
            'model_path': self.config.agent.model_path,
            'max_records': self.config.agent.max_records,
            'show_samples': self.config.agent.show_samples
        }
        
        # Remove None values
        return {k: v for k, v in args.items() if v is not None}
    
    def get_workflow_args(self) -> Dict[str, Any]:
        """
        Get command line arguments for workflow execution
        
        Returns:
            Dictionary of arguments
        """
        args = {
            'test_mode': self.config.workflow.test_mode,
            'model_path': self.config.workflow.model_path,
            'max_records': self.config.workflow.max_records,
            'save_results': self.config.workflow.save_results
        }
        
        # Remove None values
        return {k: v for k, v in args.items() if v is not None}
    
    def get_data_paths(self) -> Dict[str, str]:
        """
        Get data file paths
        
        Returns:
            Dictionary of data file paths
        """
        return asdict(self.config.data)
    
    def display_config(self):
        """Display current configuration"""
        print("="*60)
        print("CURRENT SYSTEM CONFIGURATION")
        print("="*60)
        print(f"Mode: {'Test (Rule-based)' if self.config.agent.test_mode else 'LLM'}")
        print(f"Model Path: {self.config.agent.model_path or 'N/A'}")
        print(f"Max Records: {self.config.agent.max_records or 'All'}")
        print(f"Show Samples: {self.config.agent.show_samples}")
        print(f"Save Results: {self.config.workflow.save_results}")
        print(f"Intermediate Cleanup: {self.config.workflow.intermediate_cleanup}")
        print()
        print("Data Files:")
        print(f"  Raw Logs: {self.config.data.raw_logs}")
        print(f"  CVSS Data: {self.config.data.cvss_data}")
        print(f"  Strategy Data: {self.config.data.strategy_data}")
        print("="*60)


def main():
    """Main function for configuration management"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Multi-Agent System Configuration')
    parser.add_argument('--show', action='store_true', help='Show current configuration')
    parser.add_argument('--set-test-mode', action='store_true', help='Set test mode')
    parser.add_argument('--set-llm-mode', action='store_true', help='Set LLM mode')
    parser.add_argument('--model-path', help='Set model path for LLM mode')
    parser.add_argument('--max-records', type=int, help='Set maximum records limit')
    parser.add_argument('--save', action='store_true', help='Save configuration to file')
    parser.add_argument('--config-file', default='system_config.json', help='Configuration file path')
    
    args = parser.parse_args()
    
    # Initialize configuration manager
    config_manager = ConfigManager(args.config_file)
    
    # Handle configuration changes
    if args.set_test_mode:
        config_manager.set_mode(test_mode=True)
        print("Set to test mode")
    
    if args.set_llm_mode:
        config_manager.set_mode(test_mode=False, model_path=args.model_path)
        print(f"Set to LLM mode with model: {args.model_path or 'None'}")
    
    if args.max_records is not None:
        config_manager.set_max_records(args.max_records)
        print(f"Set max records to: {args.max_records}")
    
    # Save configuration if requested
    if args.save or args.set_test_mode or args.set_llm_mode or args.max_records is not None:
        config_file = config_manager.save_config()
        print(f"Configuration saved to: {config_file}")
    
    # Show configuration
    if args.show or len(sys.argv) == 1:
        config_manager.display_config()


if __name__ == "__main__":
    main()
