# Multi-Agent System User Guide
# 多智能体系统使用指南

本文档提供多智能体网络安全系统的完整使用指南，包含安装、配置、运行和故障排除的详细说明。

## 目录 (Table of Contents)

1. [系统概述](#系统概述)
2. [环境准备](#环境准备)
3. [快速开始](#快速开始)
4. [详细使用说明](#详细使用说明)
5. [配置管理](#配置管理)
6. [故障排除](#故障排除)
7. [高级用法](#高级用法)
8. [FAQ](#faq)

---

## 系统概述

### 架构简介

这是一个基于LLM-SA框架的多智能体网络安全系统，专门用于零信任网络态势感知。系统包含4个顺序协作的智能体：

```
原始日志 → [智能体1] → [智能体2] → [智能体3] → [智能体4] → 安全策略
           威胁分类    描述生成    CVSS评分    策略生成
```

### 核心功能

- **威胁自动分类**：从原始网络日志中识别攻击类型
- **描述标准化**：生成结构化的威胁描述
- **风险量化评估**：使用CVSS 3.1标准计算风险评分
- **策略自动推荐**：基于威胁特征推荐相应的安全防护策略

### 运行模式

- **测试模式 (Test Mode)**：基于规则的快速执行，适合开发和测试
- **LLM模式 (LLM Mode)**：基于大语言模型的高质量分析

---

## 环境准备

### 系统要求

- **操作系统**：Windows 10+, macOS 10.14+, Linux (Ubuntu 18.04+)
- **Python版本**：Python 3.7 或更高版本
- **内存**：建议4GB以上
- **存储空间**：至少1GB可用空间

### 安装依赖

```bash
# 创建虚拟环境（推荐）
python -m venv agent_env
source agent_env/bin/activate  # Linux/macOS
# 或
agent_env\Scripts\activate  # Windows

# 安装必要的Python包
pip install json argparse datetime logging re math
```

### 数据文件准备

确保以下数据文件存在于正确位置：

```
agent_simplified/
├── output_0515_dataset_fin.json        # 原始威胁日志
├── output_0525_finetune_metrics.json   # CVSS训练数据
├── output_1112_strategy_train_data.json # 策略训练数据
└── agent_simplified_v2/
    └── agent_demo_v2/                   # 系统文件
```

---

## 快速开始

### 1. 交互式演示（推荐新用户）

```bash
cd agent_demo_v2
python demo_runner.py --interactive
```

按照交互式菜单提示进行操作：

```
Multi-Agent System Demo
=======================
1. Run single agent
2. Run collaborative workflow  
3. Configuration management
4. Help and documentation
5. Exit

Please select an option (1-5):
```

### 2. 一键测试完整工作流

```bash
cd agent_demo_v2
python collaborative_workflow.py --test-mode --max-records 5
```

### 3. 测试单个智能体

```bash
cd agent_demo_v2
python "Summarization Agent.py" --test-mode --max-records 10
```

---

## 详细使用说明

### 智能体1：Summarization Agent（威胁分类）

**功能**：从原始日志中识别和分类网络威胁

**独立运行**：
```bash
python "Summarization Agent.py" [选项]
```

**主要参数**：
- `--input`: 输入数据文件路径（默认：`../../output_0515_dataset_fin.json`）
- `--output`: 输出结果文件路径（默认：自动生成时间戳文件名）
- `--test-mode`: 使用测试模式（基于规则分类）
- `--llm-mode`: 使用LLM模式（需要模型路径）
- `--model-path`: LLM模型路径（LLM模式必需）

**使用示例**：
```bash
# 测试模式，处理前50条记录
python "Summarization Agent.py" --test-mode --max-records 50

# LLM模式
python "Summarization Agent.py" --llm-mode --model-path /path/to/model

# 自定义输入输出文件
python "Summarization Agent.py" --input my_data.json --output my_results.json
```

**输出格式**：
```json
{
  "total_records": 128,
  "predictions": ["DDoS", "Port Scanning", "Brute Force", ...],
  "true_categories": ["DDoS", "Port Scanning", "FTP Brute Force", ...],
  "accuracy": 0.85,
  "processing_time": "2024-01-01T12:00:00",
  "mode": "test"
}
```

### 智能体2：Prompt Agent（描述生成）

**功能**：基于完整记录生成高质量的威胁描述

**独立运行**：
```bash
python "Prompt Agent.py" [选项]
```

**主要参数**：
- `--input`: 输入数据文件路径
- `--output`: 输出结果文件路径
- `--max-records`: 限制处理的记录数量
- `--test-mode` / `--llm-mode`: 运行模式选择
- `--show-samples`: 显示的样本结果数量（默认：3）

**使用示例**：
```bash
# 生成前10条记录的描述，显示2个样本
python "Prompt Agent.py" --max-records 10 --show-samples 2

# 处理自定义数据文件
python "Prompt Agent.py" --input custom_logs.json --output enhanced_descriptions.json
```

**输出格式**：
```json
{
  "total_records": 10,
  "generated_descriptions": [
    "Distributed Denial of Service attack detected from IP **********...",
    "Port scanning activity from IP ************..."
  ],
  "original_descriptions": ["原始描述1", "原始描述2"],
  "categories": ["DDoS", "Port Scanning"],
  "processing_time": "2024-01-01T12:05:00",
  "mode": "test"
}
```

### 智能体3：Comprehensive Decision Agent（CVSS评分）

**功能**：基于威胁描述计算CVSS 3.1评分和安全指标

**独立运行**：
```bash
python "Comprehensive Decision Agent.py" [选项]
```

**主要参数**：
- `--input`: 输入数据文件路径（默认：`../../output_0525_finetune_metrics.json`）
- `--output`: 输出结果文件路径
- `--max-records`: 限制处理记录数
- `--show-samples`: 显示样本数量

**使用示例**：
```bash
# 处理CVSS数据，显示评分结果
python "Comprehensive Decision Agent.py" --max-records 20 --show-samples 3

# 使用自定义输入文件
python "Comprehensive Decision Agent.py" --input threat_descriptions.json
```

**输出格式**：
```json
{
  "total_records": 20,
  "predicted_metrics": [
    "AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:H",
    "AV:A/AC:H/PR:L/UI:R/S:C/C:H/I:H/A:H"
  ],
  "predicted_scores": [7.5, 8.1],
  "true_metrics": ["真实指标1", "真实指标2"],
  "true_scores": [7.4, 8.3],
  "strategy_inputs": [
    "DDoS Attack. CVSS Score: 7.5 (High). Metrics: AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:H",
    "Infiltration. CVSS Score: 8.1 (High). Metrics: AV:A/AC:H/PR:L/UI:R/S:C/C:H/I:H/A:H"
  ],
  "metrics_exact_match_rate": 0.75,
  "score_mae": 0.12,
  "score_rmse": 0.18
}
```

### 智能体4：Specific Advice Agent（策略生成）

**功能**：基于威胁信息和CVSS评分推荐合适的安全策略

**独立运行**：
```bash
python "Specific Advice Agent.py" [选项]
```

**主要参数**：
- `--input`: 输入数据文件路径（默认：`../../output_1112_strategy_train_data.json`）
- `--output`: 输出结果文件路径
- `--max-strategies`: 每个威胁推荐的最大策略数量（默认：5）
- `--show-samples`: 显示样本数量

**使用示例**：
```bash
# 为每个威胁生成最多3个策略建议
python "Specific Advice Agent.py" --max-strategies 3 --show-samples 2

# 处理CVSS智能体的输出结果
python "Specific Advice Agent.py" --input cvss_results.json
```

**输出格式**：
```json
{
  "total_records": 15,
  "predicted_strategies": [
    ["Implement rate limiting", "Deploy web application firewall", "Monitor network traffic"],
    ["Enable multi-factor authentication", "Implement access control policies", "Deploy intrusion detection systems"]
  ],
  "true_strategies": [
    ["Network Intrusion Prevention", "Network Segmentation", "Filter Network Traffic"],
    ["Privileged Account Management", "Multi-factor Authentication", "Access Control"]
  ],
  "exact_match_rate": 0.33,
  "partial_match_rate": 0.87,
  "avg_precision": 0.82,
  "avg_recall": 0.75,
  "f1_score": 0.78
}
```

### 协作工作流运行

**功能**：按顺序运行所有4个智能体，自动处理数据传递和格式转换

**运行命令**：
```bash
python collaborative_workflow.py [选项]
```

**主要参数**：
- `--test-mode`: 使用测试模式运行所有智能体
- `--llm-mode`: 使用LLM模式运行所有智能体
- `--model-path`: LLM模型路径（LLM模式需要）
- `--max-records`: 限制每个智能体处理的记录数
- `--save-results`: 保存工作流结果到文件（默认：开启）

**使用示例**：
```bash
# 测试模式，限制处理记录数
python collaborative_workflow.py --test-mode --max-records 10

# LLM模式，完整处理
python collaborative_workflow.py --llm-mode --model-path /path/to/model

# 快速验证（处理5条记录）
python collaborative_workflow.py --test-mode --max-records 5
```

**工作流输出**：
```json
{
  "workflow_id": "20240101_120000",
  "start_time": "2024-01-01T12:00:00",
  "end_time": "2024-01-01T12:05:00",
  "mode": "test",
  "max_records": 10,
  "success": true,
  "agents": {
    "summarization": {
      "success": true,
      "results": { "accuracy": 0.85, "total_records": 10 }
    },
    "prompt": {
      "success": true,
      "results": { "total_records": 10 }
    },
    "comprehensive": {
      "success": true,
      "results": { "metrics_exact_match_rate": 0.75, "score_mae": 0.12 }
    },
    "specific": {
      "success": true,
      "results": { "exact_match_rate": 0.33, "f1_score": 0.78 }
    }
  }
}
```

---

## 配置管理

系统提供统一的配置管理功能，通过`config.py`脚本进行管理。

### 查看当前配置

```bash
python config.py --show
```

输出示例：
```
============================================================
CURRENT SYSTEM CONFIGURATION
============================================================
Mode: Test (Rule-based)
Model Path: N/A
Max Records: All
Show Samples: 3
Save Results: True
Intermediate Cleanup: True

Data Files:
  Raw Logs: ../../output_0515_dataset_fin.json
  CVSS Data: ../../output_0525_finetune_metrics.json
  Strategy Data: ../../output_1112_strategy_train_data.json
============================================================
```

### 设置配置选项

```bash
# 设置为测试模式
python config.py --set-test-mode --save

# 设置为LLM模式，指定模型路径
python config.py --set-llm-mode --model-path /path/to/your/model --save

# 设置最大记录数限制
python config.py --max-records 100 --save

# 使用自定义配置文件
python config.py --config-file my_config.json --show
```

### 配置文件格式

系统会生成`system_config.json`配置文件：
```json
{
  "agent": {
    "test_mode": true,
    "model_path": null,
    "max_records": null,
    "show_samples": 3
  },
  "workflow": {
    "test_mode": true,
    "model_path": null,
    "max_records": null,
    "save_results": true,
    "intermediate_cleanup": true
  },
  "data": {
    "raw_logs": "../../output_0515_dataset_fin.json",
    "cvss_data": "../../output_0525_finetune_metrics.json",
    "strategy_data": "../../output_1112_strategy_train_data.json"
  }
}
```

---

## 故障排除

### 常见问题和解决方案

#### 1. 文件未找到错误
```
FileNotFoundError: Data file not found: ../../output_0515_dataset_fin.json
```

**解决方案**：
```bash
# 检查数据文件是否存在
ls -la ../../output_*.json

# 如果文件不存在，请确保：
# 1. 文件路径正确
# 2. 文件已从正确位置复制
# 3. 当前工作目录正确

cd agent_demo_v2  # 确保在正确目录
```

#### 2. Python模块导入错误
```
ModuleNotFoundError: No module named 'json'
```

**解决方案**：
```bash
# 检查Python版本
python --version

# 如果是Python 2.x，请使用Python 3
python3 "Summarization Agent.py" --help

# 或者创建虚拟环境
python -m venv agent_env
source agent_env/bin/activate
```

#### 3. 权限错误
```
PermissionError: [Errno 13] Permission denied
```

**解决方案**：
```bash
# 给脚本添加执行权限
chmod +x *.py

# 或者检查文件夹权限
ls -la
```

#### 4. 内存不足错误
```
MemoryError: Unable to allocate array
```

**解决方案**：
```bash
# 使用--max-records限制处理数据量
python "Summarization Agent.py" --max-records 50

# 或者增加系统虚拟内存
```

#### 5. 输出文件格式错误
```
json.JSONDecodeError: Expecting value: line 1 column 1
```

**解决方案**：
```bash
# 检查输入文件格式是否正确
head -n 5 ../../output_0515_dataset_fin.json

# 确保文件是有效的JSON格式
python -m json.tool ../../output_0515_dataset_fin.json > /dev/null
```

### 调试模式

开启详细日志输出：
```bash
# 设置日志级别为DEBUG
export PYTHONPATH="."
python -c "import logging; logging.basicConfig(level=logging.DEBUG)"
python "Summarization Agent.py" --test-mode --max-records 5
```

### 测试系统完整性

运行系统完整性检查：
```bash
# 测试所有智能体是否正常工作
python "Summarization Agent.py" --test-mode --max-records 2
python "Prompt Agent.py" --test-mode --max-records 2
python "Comprehensive Decision Agent.py" --test-mode --max-records 2
python "Specific Advice Agent.py" --test-mode --max-records 2

# 测试协作工作流
python collaborative_workflow.py --test-mode --max-records 2
```

---

## 高级用法

### 1. 批量处理

处理多个数据文件：
```bash
#!/bin/bash
# batch_process.sh

for file in data/*.json; do
    echo "Processing $file..."
    python "Summarization Agent.py" --input "$file" --output "results/$(basename $file .json)_summary.json"
done
```

### 2. 结果分析

使用Python脚本分析结果：
```python
# analyze_results.py
import json
import glob

def analyze_accuracy():
    result_files = glob.glob("*_results_*.json")
    
    for file in result_files:
        with open(file, 'r') as f:
            data = json.load(f)
            
        if 'accuracy' in data:
            print(f"{file}: Accuracy = {data['accuracy']:.2%}")
        if 'f1_score' in data:
            print(f"{file}: F1 Score = {data['f1_score']:.2%}")

if __name__ == "__main__":
    analyze_accuracy()
```

### 3. 自定义配置

创建专用配置文件：
```json
{
  "agent": {
    "test_mode": true,
    "max_records": 50,
    "show_samples": 5
  },
  "workflow": {
    "test_mode": true,
    "max_records": 50,
    "save_results": true
  },
  "data": {
    "raw_logs": "custom_data/my_logs.json",
    "cvss_data": "custom_data/my_cvss.json",
    "strategy_data": "custom_data/my_strategies.json"
  }
}
```

使用自定义配置：
```bash
python config.py --config-file custom_config.json --show
python collaborative_workflow.py --test-mode
```

### 4. 性能监控

监控系统性能：
```bash
# 使用time命令测量执行时间
time python collaborative_workflow.py --test-mode --max-records 100

# 监控内存使用
python -c "
import psutil
import subprocess
import time

process = subprocess.Popen(['python', 'collaborative_workflow.py', '--test-mode'])
p = psutil.Process(process.pid)

while process.poll() is None:
    memory = p.memory_info().rss / 1024 / 1024  # MB
    print(f'Memory usage: {memory:.1f} MB')
    time.sleep(1)
"
```

### 5. 结果可视化

创建简单的结果可视化：
```python
# visualize_results.py
import json
import matplotlib.pyplot as plt

def plot_accuracy_comparison():
    agents = ['Summarization', 'Comprehensive', 'Specific']
    accuracies = []
    
    # 读取各智能体结果文件
    files = {
        'Summarization': 'summarization_results_*.json',
        'Comprehensive': 'comprehensive_results_*.json', 
        'Specific': 'specific_advice_results_*.json'
    }
    
    for agent, pattern in files.items():
        # 读取最新结果文件并提取准确率
        # (具体实现省略)
        pass
    
    plt.bar(agents, accuracies)
    plt.title('Agent Performance Comparison')
    plt.ylabel('Accuracy')
    plt.show()

if __name__ == "__main__":
    plot_accuracy_comparison()
```

---

## FAQ

### Q1: 系统支持哪些攻击类型的识别？

**A**: 系统当前支持以下攻击类型：
- DDoS (分布式拒绝服务攻击)
- DoS (拒绝服务攻击)
- Infiltration (渗透攻击)
- Port Scanning (端口扫描)
- Brute Force (暴力破解)
- Botnet (僵尸网络)
- Web Attack (Web应用攻击)

可以通过修改`Summarization Agent.py`中的`classification_rules`来添加新的攻击类型。

### Q2: 如何提高分类准确率？

**A**: 提高准确率的方法：
1. **使用LLM模式**：切换到LLM模式获得更高质量的分析
2. **优化规则**：在测试模式下优化关键词规则
3. **增加训练数据**：使用更多样化的训练数据
4. **调整参数**：调整CVSS评分的阈值和权重

### Q3: 系统处理大数据集的性能如何？

**A**: 性能优化建议：
- **分批处理**：使用`--max-records`参数限制单次处理量
- **并行处理**：可以并行运行多个智能体实例
- **内存管理**：定期清理中间结果文件
- **硬件要求**：建议8GB+内存用于处理大数据集

### Q4: 如何集成到现有的安全系统中？

**A**: 集成方式：
1. **API接口**：可以将智能体封装为REST API服务
2. **文件接口**：通过JSON文件进行数据交换
3. **数据库集成**：修改数据读取部分支持数据库输入
4. **消息队列**：集成到消息队列系统中实现实时处理

### Q5: 系统的安全性如何保证？

**A**: 安全措施：
- **输入验证**：所有输入数据都经过格式验证
- **路径检查**：防止路径遍历攻击
- **权限控制**：建议在受限权限下运行
- **日志审计**：完整的操作日志记录

### Q6: 如何自定义CVSS评分规则？

**A**: 修改CVSS规则：
1. 编辑`Comprehensive Decision Agent.py`
2. 修改`CVSSScorer`类中的评分逻辑
3. 调整`attack_vector_map`、`impact_map`等映射
4. 重新训练和验证结果

### Q7: 系统支持多语言吗？

**A**: 当前系统：
- **代码**：全部使用英文
- **日志**：支持中英文混合日志分析
- **输出**：结果文件为英文格式
- **扩展**：可以通过修改模板支持其他语言

### Q8: 如何备份和恢复配置？

**A**: 配置管理：
```bash
# 备份配置
cp system_config.json backup_config_$(date +%Y%m%d).json

# 恢复配置  
cp backup_config_20240101.json system_config.json

# 重置为默认配置
rm system_config.json
python config.py --show  # 将创建默认配置
```

### Q9: 系统的许可证是什么？

**A**: 本系统为演示项目，请根据实际使用情况确定合适的许可证。建议在生产环境使用前：
- 进行充分的安全评估
- 获得适当的法律授权
- 遵守相关的数据保护法规

### Q10: 如何贡献代码或报告问题？

**A**: 参与项目：
1. **代码贡献**：遵循现有代码风格，添加适当的测试
2. **问题报告**：提供详细的错误信息和复现步骤
3. **功能建议**：描述具体的使用场景和预期效果
4. **文档改进**：帮助完善用户文档和技术文档

---

## 联系信息

如有其他问题或需要技术支持，请：

1. 查阅本用户指南的相关章节
2. 检查系统日志和错误信息
3. 参考FAQ部分的常见问题解答
4. 联系系统管理员或开发团队

---

**文档版本**: v1.0  
**最后更新**: 2024-01-01  
**适用系统版本**: Multi-Agent System Demo v2