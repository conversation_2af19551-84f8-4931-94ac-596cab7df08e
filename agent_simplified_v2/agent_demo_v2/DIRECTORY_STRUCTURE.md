# Agent Demo V2 目录结构

## 核心文件

### 🤖 Agent 文件 (4个核心Agent)
- `Summarization Agent.py` - 威胁分类和预处理Agent
- `Prompt Agent.py` - 描述生成和数据格式转换Agent  
- `Comprehensive Decision Agent.py` - CVSS评分和风险评估Agent
- `Specific Advice Agent.py` - 策略生成和最终建议Agent

### 🔧 系统文件
- `collaborative_workflow.py` - 协作工作流编排器
- `config.py` - 配置管理模块

### 📚 文档文件
- `README.md` - 项目说明和使用指南
- `INPUT_OUTPUT_EXAMPLES.md` - 详细的输入输出示例和数据格式说明
- `PROJECT_COMPLETION_SUMMARY.md` - 项目完成总结
- `DIRECTORY_STRUCTURE.md` - 本文件，目录结构说明

## 使用方式

### 单独运行Agent
```bash
python "Summarization Agent.py" --help
python "Prompt Agent.py" --help  
python "Comprehensive Decision Agent.py" --help
python "Specific Advice Agent.py" --help
```

### 运行协作工作流
```bash
python collaborative_workflow.py --help
```

## 数据文件位置
- 原始数据文件位于上级目录: `../../output_*.json`
- Agent输出文件会在当前目录生成

## 注意事项
- 所有临时演示文件已被清理
- 保留了核心功能文件和重要文档
- 目录结构简洁明了，便于维护和使用
