#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Prompt Agent - Description Generation
Standalone executable agent for description generation task

Core functionality:
- Input: Complete records from output_0515_dataset_fin.json
- Output: High-quality "Description" text
- Evaluation: Manual quality assessment
- Implementation: Bridge function, format data for downstream agents
"""

import json
import os
import sys
import argparse
from typing import List, Dict, Any, Tuple
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class DescriptionGenerator:
    """Rule-based description generator for test mode"""
    
    def __init__(self):
        # Template patterns for different attack types
        self.description_templates = {
            'DDoS': "Distributed Denial of Service attack detected from IP {ip}. Multiple sources overwhelming target with {attack_method}. Impact: {impact}. Vulnerability: {vulnerability}.",
            'DoS': "Denial of Service attack identified from IP {ip}. Single source attack using {attack_method}. Impact: {impact}. Vulnerability: {vulnerability}.",
            'Infiltration': "Infiltration attempt detected from IP {ip}. Unauthorized access via {attack_method}. Impact: {impact}. Vulnerability: {vulnerability}.",
            'PortScan': "Port scanning activity from IP {ip}. Reconnaissance using {attack_method}. Impact: {impact}. Vulnerability: {vulnerability}.",
            'BruteForce': "Brute force attack from IP {ip}. Credential attack using {attack_method}. Impact: {impact}. Vulnerability: {vulnerability}.",
            'Botnet': "Botnet activity detected from IP {ip}. Command and control via {attack_method}. Impact: {impact}. Vulnerability: {vulnerability}.",
            'Web Attack': "Web application attack from IP {ip}. Application layer attack using {attack_method}. Impact: {impact}. Vulnerability: {vulnerability}.",
        }
    
    def generate_description(self, record: List) -> str:
        """
        Generate description based on record data
        
        Args:
            record: Complete record from dataset
            
        Returns:
            Generated description text
        """
        if len(record) < 6:
            return "Insufficient data for description generation"
        
        # Extract fields from record
        # Format: [false, "ground", "Network Traffic", "DDoS", "2024-04-13 09:30:17", "description...", "Tag 1", 0]
        category = record[3] if len(record) > 3 else "Unknown"
        original_description = record[5] if len(record) > 5 else ""
        
        # Extract IP from original description if available
        ip = self.extract_ip_from_description(original_description)
        
        # Generate structured description
        if category in self.description_templates:
            template = self.description_templates[category]
            
            # Fill template with extracted information
            description = template.format(
                ip=ip or "[Unknown IP]",
                attack_method=self.extract_attack_method(original_description, category),
                impact=self.generate_impact_description(category),
                vulnerability=self.generate_vulnerability_description(category)
            )
        else:
            # Fallback for unknown categories
            description = f"Security incident detected from IP {ip or '[Unknown IP]'}. {original_description[:100]}..."
        
        return description
    
    def extract_ip_from_description(self, description: str) -> str:
        """Extract IP address from description text"""
        import re
        ip_pattern = r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'
        matches = re.findall(ip_pattern, description)
        return matches[0] if matches else None
    
    def extract_attack_method(self, description: str, category: str) -> str:
        """Extract or infer attack method based on description and category"""
        description_lower = description.lower()
        
        method_keywords = {
            'DDoS': ['flood', 'overwhelm', 'distributed'],
            'DoS': ['slowloris', 'goldeneye', 'hulk'],
            'Infiltration': ['exploit', 'privilege escalation', 'unauthorized'],
            'PortScan': ['nmap', 'scanning', 'reconnaissance'],
            'BruteForce': ['password attack', 'credential stuffing', 'dictionary'],
            'Botnet': ['command and control', 'c2', 'malware'],
            'Web Attack': ['sql injection', 'xss', 'code injection'],
        }
        
        if category in method_keywords:
            for keyword in method_keywords[category]:
                if keyword in description_lower:
                    return keyword
        
        return "unspecified method"
    
    def generate_impact_description(self, category: str) -> str:
        """Generate impact description based on attack category"""
        impact_descriptions = {
            'DDoS': "Service unavailability and resource exhaustion",
            'DoS': "Service disruption and performance degradation", 
            'Infiltration': "Potential data breach and system compromise",
            'PortScan': "Information disclosure and attack surface mapping",
            'BruteForce': "Unauthorized access and credential compromise",
            'Botnet': "System control and data exfiltration risk",
            'Web Attack': "Application compromise and data manipulation",
        }
        
        return impact_descriptions.get(category, "Security risk and potential system compromise")
    
    def generate_vulnerability_description(self, category: str) -> str:
        """Generate vulnerability description based on attack category"""
        vulnerability_descriptions = {
            'DDoS': "Insufficient rate limiting and traffic filtering",
            'DoS': "Lack of connection limits and resource protection",
            'Infiltration': "Unpatched systems and weak access controls", 
            'PortScan': "Open ports and exposed services",
            'BruteForce': "Weak passwords and missing account lockout",
            'Botnet': "Malware infection and compromised endpoints",
            'Web Attack': "Input validation flaws and insecure coding",
        }
        
        return vulnerability_descriptions.get(category, "Security configuration weaknesses")


class LLMDescriptionGenerator:
    """Mock LLM interface for description generation"""
    
    def __init__(self, model_path: str = None):
        self.model_path = model_path
        logger.info(f"LLM Description Generator initialized with model: {model_path}")
    
    def generate_description(self, record: List) -> str:
        """
        Mock LLM description generation - in real implementation, this would call actual LLM
        
        Args:
            record: Complete record from dataset
            
        Returns:
            Generated description text
        """
        # For demo purposes, use rule-based generation
        # In real implementation, this would call the LLM model
        generator = DescriptionGenerator()
        return generator.generate_description(record)


class PromptAgent:
    """Main Prompt Agent class"""
    
    def __init__(self, test_mode: bool = True, model_path: str = None):
        self.test_mode = test_mode
        self.model_path = model_path
        
        if test_mode:
            self.generator = DescriptionGenerator()
            logger.info("Initialized in test mode with rule-based generator")
        else:
            self.generator = LLMDescriptionGenerator(model_path)
            logger.info("Initialized in LLM mode")
    
    def load_data(self, file_path: str) -> List[List]:
        """
        Load threat data from JSON file
        
        Args:
            file_path: Path to the data file
            
        Returns:
            List of threat records
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Data file not found: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        logger.info(f"Loaded {len(data)} records from {file_path}")
        return data
    
    def generate_single_description(self, record: List) -> str:
        """
        Generate description for a single record
        
        Args:
            record: Single record from dataset
            
        Returns:
            Generated description
        """
        return self.generator.generate_description(record)
    
    def process_dataset(self, file_path: str, max_records: int = None) -> Dict[str, Any]:
        """
        Process entire dataset and return results
        
        Args:
            file_path: Path to input data file
            max_records: Maximum number of records to process (for testing)
            
        Returns:
            Processing results with generated descriptions
        """
        # Load data
        data = self.load_data(file_path)
        
        if max_records:
            data = data[:max_records]
            logger.info(f"Processing limited to {max_records} records")
        
        results = {
            'total_records': len(data),
            'generated_descriptions': [],
            'original_descriptions': [],
            'categories': [],
            'processing_time': datetime.now().isoformat(),
            'mode': 'test' if self.test_mode else 'llm'
        }
        
        # Process each record
        for i, record in enumerate(data):
            generated_description = self.generate_single_description(record)
            original_description = record[5] if len(record) > 5 else ""
            category = record[3] if len(record) > 3 else "Unknown"
            
            results['generated_descriptions'].append(generated_description)
            results['original_descriptions'].append(original_description)
            results['categories'].append(category)
            
            if (i + 1) % 50 == 0:
                logger.info(f"Processed {i + 1}/{len(data)} records")
        
        logger.info(f"Processing completed. Generated {len(results['generated_descriptions'])} descriptions")
        return results
    
    def save_results(self, results: Dict[str, Any], output_path: str = None) -> str:
        """
        Save results to JSON file
        
        Args:
            results: Processing results
            output_path: Output file path
            
        Returns:
            Path to saved file
        """
        if output_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"prompt_agent_results_{timestamp}.json"
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Results saved to: {output_path}")
        return output_path
    
    def display_sample_results(self, results: Dict[str, Any], num_samples: int = 3):
        """
        Display sample results for manual quality assessment
        
        Args:
            results: Processing results
            num_samples: Number of samples to display
        """
        print("\n" + "="*80)
        print("SAMPLE GENERATED DESCRIPTIONS")
        print("="*80)
        
        for i in range(min(num_samples, len(results['generated_descriptions']))):
            print(f"\nSample {i+1}:")
            print(f"Category: {results['categories'][i]}")
            print(f"Original: {results['original_descriptions'][i][:100]}...")
            print(f"Generated: {results['generated_descriptions'][i]}")
            print("-" * 80)


def main():
    """Main function for standalone execution"""
    parser = argparse.ArgumentParser(description='Prompt Agent - Description Generation')
    parser.add_argument('--input', default='../../output_0515_dataset_fin.json', 
                       help='Input data file path')
    parser.add_argument('--output', default=None, 
                       help='Output results file path')
    parser.add_argument('--max-records', type=int, default=None,
                       help='Maximum number of records to process')
    parser.add_argument('--test-mode', action='store_true', default=True,
                       help='Use test mode (rule-based generation)')
    parser.add_argument('--llm-mode', action='store_true', 
                       help='Use LLM mode')
    parser.add_argument('--model-path', default=None,
                       help='Path to LLM model (for LLM mode)')
    parser.add_argument('--show-samples', type=int, default=3,
                       help='Number of sample results to display')
    
    args = parser.parse_args()
    
    # Determine mode
    test_mode = not args.llm_mode if args.llm_mode else args.test_mode
    
    print("="*60)
    print("Prompt Agent - Description Generation")
    print("="*60)
    print(f"Mode: {'Test (Rule-based)' if test_mode else 'LLM'}")
    print(f"Input file: {args.input}")
    print(f"Max records: {args.max_records or 'All'}")
    print(f"Model path: {args.model_path if not test_mode else 'N/A'}")
    print()
    
    try:
        # Initialize agent
        agent = PromptAgent(test_mode=test_mode, model_path=args.model_path)
        
        # Process dataset
        results = agent.process_dataset(args.input, args.max_records)
        
        # Save results
        output_path = agent.save_results(results, args.output)
        
        # Display sample results
        if args.show_samples > 0:
            agent.display_sample_results(results, args.show_samples)
        
        # Display summary
        print("\n" + "="*60)
        print("PROCESSING SUMMARY")
        print("="*60)
        print(f"Total records processed: {results['total_records']}")
        print(f"Descriptions generated: {len(results['generated_descriptions'])}")
        print(f"Results saved to: {output_path}")
        print("="*60)
        print("\nNote: Manual quality assessment recommended for generated descriptions")
        
    except Exception as e:
        logger.error(f"Error during processing: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
