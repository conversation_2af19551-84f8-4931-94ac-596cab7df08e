#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Comprehensive Decision Agent - CVSS Scoring
Standalone executable agent for CVSS scoring task

Core functionality:
- Input: Threat descriptions from output_0525_finetune_metrics.json (ignore Metrics and Score fields)
- Output: Generate "Input" string for strategy generation
- Evaluation: Calculate Metrics exact match rate and Base Score accuracy
- Implementation: CVSS scoring logic
"""

import json
import os
import sys
import argparse
from typing import List, Dict, Any, Tuple
from datetime import datetime
import logging
import re

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class CVSSScorer:
    """Rule-based CVSS scorer for test mode"""
    
    def __init__(self):
        # CVSS 3.1 Base Metrics definitions
        self.attack_vector_map = {
            'network': 'N', 'adjacent': 'A', 'local': 'L', 'physical': 'P'
        }
        
        self.attack_complexity_map = {
            'low': 'L', 'high': 'H'
        }
        
        self.privileges_required_map = {
            'none': 'N', 'low': 'L', 'high': 'H'
        }
        
        self.user_interaction_map = {
            'none': 'N', 'required': 'R'
        }
        
        self.scope_map = {
            'unchanged': 'U', 'changed': 'C'
        }
        
        self.impact_map = {
            'none': 'N', 'low': 'L', 'high': 'H'
        }
        
        # Base score calculation values
        self.av_values = {'N': 0.85, 'A': 0.62, 'L': 0.55, 'P': 0.2}
        self.ac_values = {'L': 0.77, 'H': 0.44}
        self.pr_values = {'N': 0.85, 'L': 0.62, 'H': 0.27}  # Scope unchanged
        self.pr_values_changed = {'N': 0.85, 'L': 0.68, 'H': 0.5}  # Scope changed
        self.ui_values = {'N': 0.85, 'R': 0.62}
        self.impact_values = {'N': 0, 'L': 0.22, 'H': 0.56}
    
    def analyze_threat_description(self, description: str) -> Dict[str, str]:
        """
        Analyze threat description to determine CVSS metrics
        
        Args:
            description: Threat description text
            
        Returns:
            Dictionary of CVSS metrics
        """
        description_lower = description.lower()
        
        # Determine Attack Vector (AV)
        if any(keyword in description_lower for keyword in ['network', 'internet', 'remote', 'web']):
            av = 'N'
        elif any(keyword in description_lower for keyword in ['adjacent', 'lan', 'wifi']):
            av = 'A'
        elif any(keyword in description_lower for keyword in ['local', 'localhost', 'file system']):
            av = 'L'
        else:
            av = 'N'  # Default to network
        
        # Determine Attack Complexity (AC)
        if any(keyword in description_lower for keyword in ['complex', 'sophisticated', 'advanced']):
            ac = 'H'
        else:
            ac = 'L'  # Default to low
        
        # Determine Privileges Required (PR)
        if any(keyword in description_lower for keyword in ['admin', 'root', 'administrator', 'privileged']):
            pr = 'H'
        elif any(keyword in description_lower for keyword in ['user', 'authenticated', 'login']):
            pr = 'L'
        else:
            pr = 'N'  # Default to none
        
        # Determine User Interaction (UI)
        if any(keyword in description_lower for keyword in ['click', 'user interaction', 'social engineering']):
            ui = 'R'
        else:
            ui = 'N'  # Default to none
        
        # Determine Scope (S)
        if any(keyword in description_lower for keyword in ['privilege escalation', 'lateral movement', 'scope change']):
            s = 'C'
        else:
            s = 'U'  # Default to unchanged
        
        # Determine Impact metrics based on attack type
        c, i, a = self.determine_impact_metrics(description_lower)
        
        return {
            'AV': av, 'AC': ac, 'PR': pr, 'UI': ui, 'S': s,
            'C': c, 'I': i, 'A': a
        }
    
    def determine_impact_metrics(self, description_lower: str) -> Tuple[str, str, str]:
        """
        Determine Confidentiality, Integrity, and Availability impact
        
        Args:
            description_lower: Lowercase description text
            
        Returns:
            Tuple of (Confidentiality, Integrity, Availability) impact levels
        """
        # Confidentiality Impact
        if any(keyword in description_lower for keyword in ['data breach', 'information disclosure', 'leak', 'confidential']):
            c = 'H'
        elif any(keyword in description_lower for keyword in ['partial disclosure', 'limited access']):
            c = 'L'
        else:
            c = 'N'
        
        # Integrity Impact
        if any(keyword in description_lower for keyword in ['modify', 'alter', 'corrupt', 'tamper', 'integrity']):
            i = 'H'
        elif any(keyword in description_lower for keyword in ['partial modification', 'limited change']):
            i = 'L'
        else:
            i = 'N'
        
        # Availability Impact
        if any(keyword in description_lower for keyword in ['ddos', 'dos', 'unavailable', 'crash', 'shutdown']):
            a = 'H'
        elif any(keyword in description_lower for keyword in ['performance', 'slow', 'degradation']):
            a = 'L'
        else:
            a = 'N'
        
        # Default to at least low impact if no specific indicators
        if c == 'N' and i == 'N' and a == 'N':
            c = 'L'  # Assume at least some confidentiality impact
        
        return c, i, a
    
    def calculate_base_score(self, metrics: Dict[str, str]) -> float:
        """
        Calculate CVSS 3.1 Base Score
        
        Args:
            metrics: Dictionary of CVSS metrics
            
        Returns:
            Base score (0.0-10.0)
        """
        # Get metric values
        av = self.av_values[metrics['AV']]
        ac = self.ac_values[metrics['AC']]
        
        # PR depends on scope
        if metrics['S'] == 'C':
            pr = self.pr_values_changed[metrics['PR']]
        else:
            pr = self.pr_values[metrics['PR']]
        
        ui = self.ui_values[metrics['UI']]
        
        # Impact values
        c = self.impact_values[metrics['C']]
        i = self.impact_values[metrics['I']]
        a = self.impact_values[metrics['A']]
        
        # Calculate ISC (Impact Sub-score)
        isc = 1 - ((1 - c) * (1 - i) * (1 - a))
        
        # Calculate Impact
        if metrics['S'] == 'U':
            impact = 6.42 * isc
        else:
            impact = 7.52 * (isc - 0.029) - 3.25 * pow(isc - 0.02, 15)
        
        # Calculate Exploitability
        exploitability = 8.22 * av * ac * pr * ui
        
        # Calculate Base Score
        if impact <= 0:
            base_score = 0.0
        else:
            if metrics['S'] == 'U':
                base_score = min(impact + exploitability, 10.0)
            else:
                base_score = min(1.08 * (impact + exploitability), 10.0)
        
        # Round to one decimal place
        return round(base_score, 1)
    
    def generate_metrics_string(self, metrics: Dict[str, str]) -> str:
        """
        Generate CVSS metrics string
        
        Args:
            metrics: Dictionary of CVSS metrics
            
        Returns:
            CVSS metrics string (e.g., "AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N")
        """
        return f"AV:{metrics['AV']}/AC:{metrics['AC']}/PR:{metrics['PR']}/UI:{metrics['UI']}/S:{metrics['S']}/C:{metrics['C']}/I:{metrics['I']}/A:{metrics['A']}"
    
    def score_threat(self, description: str) -> Dict[str, Any]:
        """
        Score a single threat description
        
        Args:
            description: Threat description text
            
        Returns:
            Dictionary with metrics and score
        """
        metrics = self.analyze_threat_description(description)
        base_score = self.calculate_base_score(metrics)
        metrics_string = self.generate_metrics_string(metrics)
        
        return {
            'metrics': metrics_string,
            'base_score': base_score,
            'individual_metrics': metrics
        }


class LLMCVSSScorer:
    """Mock LLM interface for CVSS scoring"""
    
    def __init__(self, model_path: str = None):
        self.model_path = model_path
        logger.info(f"LLM CVSS Scorer initialized with model: {model_path}")
    
    def score_threat(self, description: str) -> Dict[str, Any]:
        """
        Mock LLM CVSS scoring - in real implementation, this would call actual LLM
        
        Args:
            description: Threat description text
            
        Returns:
            Dictionary with metrics and score
        """
        # For demo purposes, use rule-based scoring
        # In real implementation, this would call the LLM model
        scorer = CVSSScorer()
        return scorer.score_threat(description)


class ComprehensiveDecisionAgent:
    """Main Comprehensive Decision Agent class"""
    
    def __init__(self, test_mode: bool = True, model_path: str = None):
        self.test_mode = test_mode
        self.model_path = model_path
        
        if test_mode:
            self.scorer = CVSSScorer()
            logger.info("Initialized in test mode with rule-based CVSS scorer")
        else:
            self.scorer = LLMCVSSScorer(model_path)
            logger.info("Initialized in LLM mode")
    
    def load_data(self, file_path: str) -> List[Dict[str, Any]]:
        """
        Load CVSS data from JSON file
        
        Args:
            file_path: Path to the data file
            
        Returns:
            List of CVSS records
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Data file not found: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        logger.info(f"Loaded {len(data)} records from {file_path}")
        return data
    
    def extract_description(self, record: Dict[str, Any]) -> str:
        """
        Extract description from CVSS record
        
        Args:
            record: Single record from the dataset
            
        Returns:
            Description text
        """
        return record.get('Description', '')
    
    def get_true_metrics(self, record: Dict[str, Any]) -> str:
        """
        Extract true CVSS metrics from record (for evaluation)
        
        Args:
            record: Single record from the dataset
            
        Returns:
            True CVSS metrics string
        """
        return record.get('Metrics', '')
    
    def get_true_score(self, record: Dict[str, Any]) -> float:
        """
        Extract true base score from record (for evaluation)
        
        Args:
            record: Single record from the dataset
            
        Returns:
            True base score
        """
        return float(record.get('Base Score', 0.0))
    
    def score_single_threat(self, description: str) -> Dict[str, Any]:
        """
        Score a single threat description
        
        Args:
            description: Threat description text
            
        Returns:
            Scoring results
        """
        return self.scorer.score_threat(description)
    
    def generate_strategy_input(self, description: str, metrics: str, score: float) -> str:
        """
        Generate input string for strategy generation agent
        
        Args:
            description: Threat description
            metrics: CVSS metrics string
            score: Base score
            
        Returns:
            Formatted input string for strategy agent
        """
        # Extract key information for strategy generation
        threat_type = self.extract_threat_type(description)
        severity = self.get_severity_level(score)
        
        input_string = f"Threat Type: {threat_type}. Description: {description}. CVSS Score: {score} ({severity}). Metrics: {metrics}."
        return input_string
    
    def extract_threat_type(self, description: str) -> str:
        """Extract threat type from description"""
        description_lower = description.lower()
        
        if 'ddos' in description_lower or 'distributed denial' in description_lower:
            return 'DDoS Attack'
        elif 'dos' in description_lower or 'denial of service' in description_lower:
            return 'DoS Attack'
        elif 'infiltration' in description_lower or 'unauthorized access' in description_lower:
            return 'Infiltration'
        elif 'brute force' in description_lower or 'password attack' in description_lower:
            return 'Brute Force Attack'
        elif 'web attack' in description_lower or 'sql injection' in description_lower:
            return 'Web Application Attack'
        elif 'port scan' in description_lower or 'reconnaissance' in description_lower:
            return 'Port Scanning'
        elif 'botnet' in description_lower or 'malware' in description_lower:
            return 'Botnet Activity'
        else:
            return 'Security Incident'
    
    def get_severity_level(self, score: float) -> str:
        """Get severity level based on CVSS score"""
        if score >= 9.0:
            return 'Critical'
        elif score >= 7.0:
            return 'High'
        elif score >= 4.0:
            return 'Medium'
        elif score > 0.0:
            return 'Low'
        else:
            return 'None'
    
    def process_dataset(self, file_path: str, max_records: int = None) -> Dict[str, Any]:
        """
        Process entire dataset and return results
        
        Args:
            file_path: Path to input data file
            max_records: Maximum number of records to process (for testing)
            
        Returns:
            Processing results with CVSS scores and metrics
        """
        # Load data
        data = self.load_data(file_path)
        
        if max_records:
            data = data[:max_records]
            logger.info(f"Processing limited to {max_records} records")
        
        results = {
            'total_records': len(data),
            'predicted_metrics': [],
            'predicted_scores': [],
            'true_metrics': [],
            'true_scores': [],
            'strategy_inputs': [],
            'processing_time': datetime.now().isoformat(),
            'mode': 'test' if self.test_mode else 'llm'
        }
        
        # Process each record
        for i, record in enumerate(data):
            description = self.extract_description(record)
            true_metrics = self.get_true_metrics(record)
            true_score = self.get_true_score(record)
            
            # Score the threat
            scoring_result = self.score_single_threat(description)
            predicted_metrics = scoring_result['metrics']
            predicted_score = scoring_result['base_score']
            
            # Generate strategy input
            strategy_input = self.generate_strategy_input(description, predicted_metrics, predicted_score)
            
            results['predicted_metrics'].append(predicted_metrics)
            results['predicted_scores'].append(predicted_score)
            results['true_metrics'].append(true_metrics)
            results['true_scores'].append(true_score)
            results['strategy_inputs'].append(strategy_input)
            
            if (i + 1) % 50 == 0:
                logger.info(f"Processed {i + 1}/{len(data)} records")
        
        # Calculate evaluation metrics
        metrics_accuracy = self.calculate_metrics_accuracy(results['true_metrics'], results['predicted_metrics'])
        score_accuracy = self.calculate_score_accuracy(results['true_scores'], results['predicted_scores'])
        
        results['metrics_exact_match_rate'] = metrics_accuracy
        results['score_mae'] = score_accuracy['mae']
        results['score_rmse'] = score_accuracy['rmse']
        
        logger.info(f"Processing completed. Metrics accuracy: {metrics_accuracy:.2%}, Score MAE: {score_accuracy['mae']:.2f}")
        return results
    
    def calculate_metrics_accuracy(self, true_metrics: List[str], predicted_metrics: List[str]) -> float:
        """
        Calculate exact match rate for CVSS metrics
        
        Args:
            true_metrics: List of true CVSS metrics strings
            predicted_metrics: List of predicted CVSS metrics strings
            
        Returns:
            Exact match rate (0-1)
        """
        if len(true_metrics) != len(predicted_metrics):
            raise ValueError("Length mismatch between true and predicted metrics")
        
        exact_matches = sum(1 for true, pred in zip(true_metrics, predicted_metrics) if true == pred)
        return exact_matches / len(true_metrics) if true_metrics else 0.0
    
    def calculate_score_accuracy(self, true_scores: List[float], predicted_scores: List[float]) -> Dict[str, float]:
        """
        Calculate accuracy metrics for CVSS scores
        
        Args:
            true_scores: List of true base scores
            predicted_scores: List of predicted base scores
            
        Returns:
            Dictionary with MAE and RMSE
        """
        if len(true_scores) != len(predicted_scores):
            raise ValueError("Length mismatch between true and predicted scores")
        
        import math
        
        # Calculate Mean Absolute Error (MAE)
        mae = sum(abs(true - pred) for true, pred in zip(true_scores, predicted_scores)) / len(true_scores)
        
        # Calculate Root Mean Square Error (RMSE)
        mse = sum((true - pred) ** 2 for true, pred in zip(true_scores, predicted_scores)) / len(true_scores)
        rmse = math.sqrt(mse)
        
        return {'mae': mae, 'rmse': rmse}
    
    def save_results(self, results: Dict[str, Any], output_path: str = None) -> str:
        """
        Save results to JSON file
        
        Args:
            results: Processing results
            output_path: Output file path
            
        Returns:
            Path to saved file
        """
        if output_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"comprehensive_decision_results_{timestamp}.json"
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Results saved to: {output_path}")
        return output_path
    
    def display_sample_results(self, results: Dict[str, Any], num_samples: int = 3):
        """
        Display sample results for manual quality assessment
        
        Args:
            results: Processing results
            num_samples: Number of samples to display
        """
        print("\n" + "="*80)
        print("SAMPLE CVSS SCORING RESULTS")
        print("="*80)
        
        for i in range(min(num_samples, len(results['predicted_metrics']))):
            print(f"\nSample {i+1}:")
            print(f"True Metrics: {results['true_metrics'][i]}")
            print(f"Predicted Metrics: {results['predicted_metrics'][i]}")
            print(f"True Score: {results['true_scores'][i]}")
            print(f"Predicted Score: {results['predicted_scores'][i]}")
            print(f"Strategy Input: {results['strategy_inputs'][i][:100]}...")
            print("-" * 80)


def main():
    """Main function for standalone execution"""
    parser = argparse.ArgumentParser(description='Comprehensive Decision Agent - CVSS Scoring')
    parser.add_argument('--input', default='../../output_0525_finetune_metrics.json', 
                       help='Input data file path')
    parser.add_argument('--output', default=None, 
                       help='Output results file path')
    parser.add_argument('--max-records', type=int, default=None,
                       help='Maximum number of records to process')
    parser.add_argument('--test-mode', action='store_true', default=True,
                       help='Use test mode (rule-based CVSS scoring)')
    parser.add_argument('--llm-mode', action='store_true', 
                       help='Use LLM mode')
    parser.add_argument('--model-path', default=None,
                       help='Path to LLM model (for LLM mode)')
    parser.add_argument('--show-samples', type=int, default=3,
                       help='Number of sample results to display')
    
    args = parser.parse_args()
    
    # Determine mode
    test_mode = not args.llm_mode if args.llm_mode else args.test_mode
    
    print("="*60)
    print("Comprehensive Decision Agent - CVSS Scoring")
    print("="*60)
    print(f"Mode: {'Test (Rule-based)' if test_mode else 'LLM'}")
    print(f"Input file: {args.input}")
    print(f"Max records: {args.max_records or 'All'}")
    print(f"Model path: {args.model_path if not test_mode else 'N/A'}")
    print()
    
    try:
        # Initialize agent
        agent = ComprehensiveDecisionAgent(test_mode=test_mode, model_path=args.model_path)
        
        # Process dataset
        results = agent.process_dataset(args.input, args.max_records)
        
        # Save results
        output_path = agent.save_results(results, args.output)
        
        # Display sample results
        if args.show_samples > 0:
            agent.display_sample_results(results, args.show_samples)
        
        # Display summary
        print("\n" + "="*60)
        print("PROCESSING SUMMARY")
        print("="*60)
        print(f"Total records processed: {results['total_records']}")
        print(f"Metrics exact match rate: {results['metrics_exact_match_rate']:.2%}")
        print(f"Score MAE: {results['score_mae']:.2f}")
        print(f"Score RMSE: {results['score_rmse']:.2f}")
        print(f"Results saved to: {output_path}")
        print("="*60)
        
    except Exception as e:
        logger.error(f"Error during processing: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
