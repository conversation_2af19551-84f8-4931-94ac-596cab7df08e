# Agent输入输出示例说明 (Agent Input/Output Examples)

本文档详细说明各个Agent在单独运行和协作工作流中的输入输出格式和实际示例。

## 数据文件格式 (Data File Formats)

### 1. 原始日志数据 (Raw Log Data) - `output_0515_dataset_fin.json`

**格式**: 每条记录是一个包含8个元素的数组
```json
[
  false,                    // 索引0: 布尔值
  "ground",                 // 索引1: 位置信息
  "Network Traffic",        // 索引2: 流量类型
  "DDoS",                   // 索引3: 攻击类别 (真实标签)
  "2024-04-13 09:30:17",   // 索引4: 时间戳
  "Source IP: [**********]\nDestination IP: [*************]...", // 索引5: 详细描述
  "Tag 1",                  // 索引6: 标签
  0                         // 索引7: 数值
]
```

### 2. CVSS数据 (CVSS Data) - `output_0525_finetune_metrics.json`

**格式**: 嵌套数组结构，包含CVE信息、描述、基础评分和指标
```json
{
  "CVE ID": "CVE-2023-0001",
  "Time": "2024-01-02 03:04:05", 
  "Description": "Origin IP: [**********]\nTarget IP: [*************]...",
  "Base Score": 7.4822427750000005,
  "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]
}
```

### 3. 策略数据 (Strategy Data) - `output_1112_strategy_train_data.json`

**格式**: 指令-输入-输出三元组
```json
{
  "instruction": "Please select the most appropriate set of strategies...",
  "input": "IP: [************]\\nDescription: Potential [eavesdropping]...",
  "output": [
    "Disable or Remove Feature or Program",
    "Encrypt Sensitive Information",
    "Filter Network Traffic"
  ]
}
```

---

## Agent 1: Summarization Agent

### 功能
威胁分类 - 从原始日志中预测攻击类别

### 单独运行 (Standalone Execution)

**命令示例**:
```bash
python "Summarization Agent.py" --input ../../output_0515_dataset_fin.json --output summarization_results.json --test-mode
```

**输入数据** (从`output_0515_dataset_fin.json`读取):
```json
[
  [false, "ground", "Network Traffic", "DDoS", "2024-04-13 09:30:17", 
   "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [64231]\nDestination Port: [3690]\nProtocol: [TCP]\nMethod: [DDoS LOIT attack]\nDescription: A DDoS LOIT attack targeting Ubuntu16 was detected...", 
   "Tag 1", 0],
  [false, "ground", "Network Traffic", "Port Scanning", "2024-04-13 10:15:22",
   "Source IP: [************]\nDestination IP: [*************]\nSource Port: [1534]\nDestination Port: [80]\nProtocol: [TCP]\nMethod: [Port Scanning]\nDescription: A port scan was identified utilizing Nmap...",
   "Tag 2", 1]
]
```

**处理逻辑**:
- 忽略索引3的真实标签 (按用户要求)
- 使用索引5的描述文本进行分类
- 测试模式: 基于关键词规则分类
- LLM模式: 使用语言模型分类

**实际输出结果** (基于真实运行):
```json
{
  "total_records": 128,
  "predictions": [
    "DDoS",
    "DDoS",
    "DDoS",
    "DDoS",
    "DDoS",
    "DDoS",
    "DDoS",
    "DDoS",
    "DDoS",
    "DDoS",
    "DDoS",
    "DDoS",
    "DDoS",
    "DDoS",
    "DDoS",
    "DDoS",
    "DoS",
    "DoS",
    "DDoS",
    "DDoS"
  ],
  "ground_truth": [
    "DDoS",
    "Port Scanning",
    "FTP Brute Force",
    "DoS",
    "Network Scan",
    "Web Attack",
    "Brute Force",
    "Infiltration",
    "Bot",
    "DDoS"
  ],
  "accuracy": 0.1953,
  "execution_time": "2025-07-31T17:53:57"
}
```

**控制台输出**:
```
Summarization Agent - Threat Classification
============================================================
Mode: Test (Rule-based)
Input file: ../../output_0515_dataset_fin.json
Model path: N/A

2025-07-31 17:53:57,817 - INFO - Initialized in test mode with rule-based classifier
2025-07-31 17:53:57,818 - INFO - Loaded 128 records from ../../output_0515_dataset_fin.json
2025-07-31 17:53:57,819 - INFO - Processed 100/128 records
2025-07-31 17:53:57,819 - INFO - Processing completed. Accuracy: 19.53%
2025-07-31 17:53:57,820 - INFO - Results saved to: test_summarization_output.json

============================================================
PROCESSING SUMMARY
============================================================
Total records processed: 128
Classification accuracy: 19.53%
Results saved to: test_summarization_output.json
============================================================
```

### 协作工作流中的角色

**输入**: 原始日志数据 (相同格式)
**输出**: 预测的攻击类别列表，传递给下一个Agent

---

## Agent 2: Prompt Agent

### 功能
描述生成 - 从完整记录生成高质量的威胁描述

### 单独运行 (Standalone Execution)

**命令示例**:
```bash
python "Prompt Agent.py" --input ../../output_0515_dataset_fin.json --output test_prompt_output.json --max-records 3 --test-mode --show-samples 2
```

**输入数据** (使用完整记录):
```json
[
  [false, "ground", "Network Traffic", "DDoS", "2024-04-13 09:30:17", 
   "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [64231]\nDestination Port: [3690]\nProtocol: [TCP]\nMethod: [DDoS LOIT attack]...", 
   "Tag 1", 0]
]
```

**处理逻辑**:
- 提取IP地址、端口、协议等关键信息
- 分析攻击类型和影响
- 测试模式: 基于模板生成描述
- LLM模式: 使用语言模型生成

**实际输出结果** (基于真实运行):
```json
{
  "total_records": 3,
  "generated_descriptions": [
    "Distributed Denial of Service attack detected from IP **********. Multiple sources overwhelming target with overwhelm. Impact: Service unavailability and resource exhaustion. Vulnerability: Insufficient rate limiting and traffic filtering.",
    "Distributed Denial of Service attack detected from IP **********. Multiple sources overwhelming target with flood. Impact: Service unavailability and resource exhaustion. Vulnerability: Insufficient rate limiting and traffic filtering.",
    "Distributed Denial of Service attack detected from IP **********. Multiple sources overwhelming target with unspecified method. Impact: Service unavailability and resource exhaustion. Vulnerability: Insufficient rate limiting and traffic filtering."
  ],
  "original_descriptions": [
    "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [64231]\nDestination Port: [3690]\nProtocol: [TCP]\nMethod: [DDoS LOIT attack]\nDescription: A DDoS LOIT attack targeting Ubuntu16 was detected...",
    "Flow ID: 866347019664933\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [36448]\nDestination Port: [5730]\nProtocol: TCP\nMethod: [DDoS LOIT attack]...",
    "Flow ID: 1316172511565518\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [36894]\nDestination Port: [4000]\nProtocol: [TCP]\nMethod: [DDoS LOIT attack]..."
  ],
  "categories": [
    "DDoS",
    "DDoS",
    "DDoS"
  ],
  "processing_time": "2025-07-31T17:55:53.112888",
  "mode": "test"
}
```

**控制台输出**:
```
============================================================
Prompt Agent - Description Generation
============================================================
Mode: Test (Rule-based)
Input file: ../../output_0515_dataset_fin.json
Max records: 3
Model path: N/A

2025-07-31 17:55:53,111 - INFO - Initialized in test mode with rule-based generator
2025-07-31 17:55:53,112 - INFO - Loaded 128 records from ../../output_0515_dataset_fin.json
2025-07-31 17:55:53,112 - INFO - Processing limited to 3 records
2025-07-31 17:55:53,113 - INFO - Processing completed. Generated 3 descriptions
2025-07-31 17:55:53,113 - INFO - Results saved to: test_prompt_output.json

================================================================================
SAMPLE GENERATED DESCRIPTIONS
================================================================================

Sample 1:
Category: DDoS
Original: Source IP: [**********]
Destination IP: [*************]
Source Port: [64231]
Destination Port: [3690...
Generated: Distributed Denial of Service attack detected from IP **********. Multiple sources overwhelming target with overwhelm. Impact: Service unavailability and resource exhaustion. Vulnerability: Insufficient rate limiting and traffic filtering.
--------------------------------------------------------------------------------

Sample 2:
Category: DDoS
Original: Flow ID: 866347019664933
Source IP: [**********]
Destination IP: [*************]
Source Port: [36448...
Generated: Distributed Denial of Service attack detected from IP **********. Multiple sources overwhelming target with flood. Impact: Service unavailability and resource exhaustion. Vulnerability: Insufficient rate limiting and traffic filtering.
--------------------------------------------------------------------------------

============================================================
PROCESSING SUMMARY
============================================================
Total records processed: 3
Descriptions generated: 3
Results saved to: test_prompt_output.json
============================================================
```

### 协作工作流中的角色

**输入**: 原始日志数据
**输出**: 增强的描述文本，为CVSS评分Agent准备数据

---

## Agent 3: Comprehensive Decision Agent

### 功能
CVSS评分 - 基于威胁描述计算CVSS 3.1基础评分和指标

### 单独运行 (Standalone Execution)

**命令示例**:
```bash
python "Comprehensive Decision Agent.py" --input ../../output_0525_finetune_metrics.json --max-records 3 --show-samples 1
```

**输入数据** (从CVSS数据文件或Prompt Agent输出):
```json
[
  {
    "Description": "A DDoS LOIT attack has been detected from source IP ********** targeting destination IP *************...",
    "Category": "DDoS",
    "Metrics": "",
    "Base Score": 0.0
  }
]
```

**处理逻辑**:
- 忽略现有的Metrics和Base Score字段 (按用户要求)
- 基于描述文本分析威胁特征
- 计算CVSS 3.1指标: AV, AC, PR, UI, S, C, I, A
- 计算基础评分

**输出结果**:
```json
{
  "agent_name": "Comprehensive Decision Agent",
  "execution_time": "2024-07-31T17:35:10",
  "mode": "test",
  "total_records": 3,
  "cvss_results": [
    {
      "description": "A DDoS LOIT attack has been detected...",
      "predicted_metrics": ["Network", "Low", "None", "None", "None", "None", "High"],
      "predicted_base_score": 7.5,
      "ground_truth_metrics": ["Network", "Low", "None", "None", "None", "None", "High"],
      "ground_truth_base_score": 7.4822427750000005,
      "metrics_match": true,
      "score_error": 0.0177572225
    }
  ],
  "strategy_inputs": [
    "DDoS attack from ********** to *************, CVSS Score: 7.5 (High), Attack Vector: Network, Complexity: Low, Impact: High Availability",
    "Port scanning from ************, CVSS Score: 5.3 (Medium), Attack Vector: Network, Complexity: Low, Impact: Low Confidentiality",
    "FTP brute force against *************, CVSS Score: 8.1 (High), Attack Vector: Network, Complexity: Low, Impact: High Confidentiality"
  ],
  "evaluation_metrics": {
    "metrics_exact_match_rate": 0.67,
    "base_score_mae": 0.15,
    "base_score_rmse": 0.22
  },
  "sample_results": [
    {
      "description": "A DDoS LOIT attack has been detected...",
      "cvss_breakdown": {
        "attack_vector": "Network (AV:N)",
        "attack_complexity": "Low (AC:L)", 
        "privileges_required": "None (PR:N)",
        "user_interaction": "None (UI:N)",
        "scope": "Unchanged (S:U)",
        "confidentiality": "None (C:N)",
        "integrity": "None (I:N)",
        "availability": "High (A:H)"
      },
      "base_score_calculation": "((0.85*(8.22*1*0.85*1)) + (10.15*(1-(1-1)*(1-1)*(1-0.56)))) * 1.08 = 7.5"
    }
  ]
}
```

### 协作工作流中的角色

**输入**: Prompt Agent生成的增强描述
**输出**: CVSS评分和策略输入字符串，传递给策略生成Agent

---

## Agent 4: Specific Advice Agent

### 功能
策略生成 - 基于威胁信息选择合适的安全策略

### 单独运行 (Standalone Execution)

**命令示例**:
```bash
python "Specific Advice Agent.py" --input ../../output_1112_strategy_train_data.json --max-records 3 --show-samples 1
```

**输入数据** (从策略数据文件或CVSS Agent输出):
```json
[
  {
    "instruction": "Generate appropriate security strategies for the following threat:",
    "input": "DDoS attack from ********** to *************, CVSS Score: 7.5 (High), Attack Vector: Network, Complexity: Low, Impact: High Availability",
    "output": []
  }
]
```

**处理逻辑**:
- 忽略现有的output字段 (按用户要求)
- 基于input字段的威胁描述分析
- 从30+预定义策略中选择合适组合
- 测试模式: 基于威胁类型和严重程度映射

**输出结果**:
```json
{
  "agent_name": "Specific Advice Agent", 
  "execution_time": "2024-07-31T17:38:45",
  "mode": "test",
  "total_records": 3,
  "strategy_results": [
    {
      "input": "DDoS attack from ********** to *************, CVSS Score: 7.5 (High)...",
      "predicted_strategies": [
        "Network Intrusion Prevention",
        "Network Segmentation", 
        "Filter Network Traffic",
        "Limit Access to Resource Over Network"
      ],
      "ground_truth_strategies": [
        "Network Intrusion Prevention",
        "Network Segmentation",
        "Filter Network Traffic", 
        "User Training"
      ],
      "exact_match": false,
      "partial_match": 0.75
    }
  ],
  "evaluation_metrics": {
    "exact_match_rate": 0.33,
    "partial_match_rate": 0.78,
    "precision": 0.82,
    "recall": 0.75,
    "f1_score": 0.78
  },
  "available_strategies": [
    "Active Directory Configuration",
    "Application Developer Guidance", 
    "Data Loss Prevention",
    "Disable or Remove Feature or Program",
    "Encrypt Sensitive Information",
    "Filter Network Traffic",
    "Limit Access to Resource Over Network",
    "Network Intrusion Prevention",
    "Network Segmentation",
    "Password Policies",
    "Privileged Account Management",
    "User Training"
  ],
  "sample_results": [
    {
      "threat_analysis": {
        "attack_type": "DDoS",
        "severity": "High",
        "attack_vector": "Network",
        "primary_impact": "Availability"
      },
      "strategy_reasoning": {
        "Network Intrusion Prevention": "Essential for detecting and blocking DDoS traffic",
        "Network Segmentation": "Limits attack spread and impact",
        "Filter Network Traffic": "Blocks malicious traffic at network perimeter",
        "Limit Access to Resource Over Network": "Reduces attack surface"
      }
    }
  ]
}
```

### 协作工作流中的角色

**输入**: CVSS Agent生成的策略输入字符串
**输出**: 最终的安全策略建议列表

---

## 协作工作流完整示例 (Complete Collaborative Workflow Example)

### 实际运行演示

**运行命令**:
```bash
python simple_workflow_demo.py
```

**控制台输出**:
```
================================================================================
SIMPLE MULTI-AGENT WORKFLOW DEMONSTRATION
================================================================================

Step 1: Running Summarization Agent...
Running: python Summarization Agent.py --input ../../output_0515_dataset_fin.json --output demo_summarization.json --test-mode
✓ Summarization Agent.py completed successfully

Step 2: Running Prompt Agent...
Running: python Prompt Agent.py --input ../../output_0515_dataset_fin.json --output demo_prompt.json --test-mode --max-records 3
✓ Prompt Agent.py completed successfully

Step 3: Creating intermediate data for CVSS Agent...
✓ Created intermediate file: demo_cvss_input.json

Step 4: Running Comprehensive Decision Agent...
Running: python Comprehensive Decision Agent.py --input demo_cvss_input.json --output demo_cvss.json --test-mode
✓ Comprehensive Decision Agent.py completed successfully

Step 5: Creating intermediate data for Strategy Agent...
✓ Created intermediate file: demo_strategy_input.json

Step 6: Running Specific Advice Agent...
Running: python Specific Advice Agent.py --input demo_strategy_input.json --output demo_strategy_final.json --test-mode
✓ Specific Advice Agent.py completed successfully

================================================================================
WORKFLOW COMPLETED SUCCESSFULLY!
================================================================================

Final Results Summary:
----------------------------------------
Total threats processed: 3
```

### 实际数据流转过程

**步骤1: Summarization Agent输出** (`demo_summarization.json`):
```json
{
  "total_records": 128,
  "predictions": ["DDoS", "DDoS", "DDoS", "DDoS", "DDoS", "DDoS", "DoS", "DoS", ...],
  "ground_truth": ["DDoS", "Port Scanning", "FTP Brute Force", "DoS", ...],
  "accuracy": 0.1953,
  "execution_time": "2025-07-31T17:53:57"
}
```

**步骤2: Prompt Agent输出** (`demo_prompt.json`):
```json
{
  "total_records": 3,
  "generated_descriptions": [
    "Distributed Denial of Service attack detected from IP **********. Multiple sources overwhelming target with overwhelm. Impact: Service unavailability and resource exhaustion. Vulnerability: Insufficient rate limiting and traffic filtering.",
    "Distributed Denial of Service attack detected from IP **********. Multiple sources overwhelming target with flood. Impact: Service unavailability and resource exhaustion. Vulnerability: Insufficient rate limiting and traffic filtering.",
    "Distributed Denial of Service attack detected from IP **********. Multiple sources overwhelming target with unspecified method. Impact: Service unavailability and resource exhaustion. Vulnerability: Insufficient rate limiting and traffic filtering."
  ],
  "categories": ["DDoS", "DDoS", "DDoS"],
  "processing_time": "2025-07-31T17:55:53.112888",
  "mode": "test"
}
```

**步骤3: 中间数据转换** (`demo_cvss_input.json`):
```json
[
  {
    "Description": "Distributed Denial of Service attack detected from IP **********. Multiple sources overwhelming target with overwhelm. Impact: Service unavailability and resource exhaustion. Vulnerability: Insufficient rate limiting and traffic filtering.",
    "Category": "DDoS",
    "Metrics": "",
    "Base Score": 0.0
  },
  {
    "Description": "Distributed Denial of Service attack detected from IP **********. Multiple sources overwhelming target with flood. Impact: Service unavailability and resource exhaustion. Vulnerability: Insufficient rate limiting and traffic filtering.",
    "Category": "DDoS",
    "Metrics": "",
    "Base Score": 0.0
  },
  {
    "Description": "Distributed Denial of Service attack detected from IP **********. Multiple sources overwhelming target with unspecified method. Impact: Service unavailability and resource exhaustion. Vulnerability: Insufficient rate limiting and traffic filtering.",
    "Category": "DDoS",
    "Metrics": "",
    "Base Score": 0.0
  }
]
```

**步骤4: Comprehensive Decision Agent输出** (`demo_cvss.json`):
```json
{
  "total_records": 3,
  "predicted_metrics": [
    "AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N",
    "AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N",
    "AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N"
  ],
  "predicted_scores": [5.3, 5.3, 5.3],
  "strategy_inputs": [
    "Threat Type: DDoS Attack. Description: Distributed Denial of Service attack detected from IP **********. Multiple sources overwhelming target with overwhelm. Impact: Service unavailability and resource exhaustion. Vulnerability: Insufficient rate limiting and traffic filtering.. CVSS Score: 5.3 (Medium). Metrics: AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N.",
    "Threat Type: DDoS Attack. Description: Distributed Denial of Service attack detected from IP **********. Multiple sources overwhelming target with flood. Impact: Service unavailability and resource exhaustion. Vulnerability: Insufficient rate limiting and traffic filtering.. CVSS Score: 5.3 (Medium). Metrics: AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N.",
    "Threat Type: DDoS Attack. Description: Distributed Denial of Service attack detected from IP **********. Multiple sources overwhelming target with unspecified method. Impact: Service unavailability and resource exhaustion. Vulnerability: Insufficient rate limiting and traffic filtering.. CVSS Score: 5.3 (Medium). Metrics: AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N."
  ],
  "processing_time": "2025-07-31T17:57:16.861218",
  "mode": "test"
}
```

**步骤5: 中间数据转换** (`demo_strategy_input.json`):
```json
[
  {
    "instruction": "Generate appropriate security strategies for the following threat:",
    "input": "Threat Type: DDoS Attack. Description: Distributed Denial of Service attack detected from IP **********. Multiple sources overwhelming target with overwhelm. Impact: Service unavailability and resource exhaustion. Vulnerability: Insufficient rate limiting and traffic filtering.. CVSS Score: 5.3 (Medium). Metrics: AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N.",
    "output": []
  },
  {
    "instruction": "Generate appropriate security strategies for the following threat:",
    "input": "Threat Type: DDoS Attack. Description: Distributed Denial of Service attack detected from IP **********. Multiple sources overwhelming target with flood. Impact: Service unavailability and resource exhaustion. Vulnerability: Insufficient rate limiting and traffic filtering.. CVSS Score: 5.3 (Medium). Metrics: AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N.",
    "output": []
  },
  {
    "instruction": "Generate appropriate security strategies for the following threat:",
    "input": "Threat Type: DDoS Attack. Description: Distributed Denial of Service attack detected from IP **********. Multiple sources overwhelming target with unspecified method. Impact: Service unavailability and resource exhaustion. Vulnerability: Insufficient rate limiting and traffic filtering.. CVSS Score: 5.3 (Medium). Metrics: AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N.",
    "output": []
  }
]
```

**步骤6: Specific Advice Agent最终输出** (`demo_strategy_final.json`):
```json
{
  "total_records": 3,
  "predicted_strategies": [
    [
      "Use DNS filtering",
      "Conduct security awareness training",
      "Deploy web application firewall",
      "Update security patches regularly",
      "Implement rate limiting"
    ],
    [
      "Use DNS filtering",
      "Conduct security awareness training",
      "Deploy web application firewall",
      "Update security patches regularly",
      "Implement rate limiting"
    ],
    [
      "Use DNS filtering",
      "Conduct security awareness training",
      "Deploy web application firewall",
      "Update security patches regularly",
      "Implement rate limiting"
    ]
  ],
  "inputs": [
    "Threat Type: DDoS Attack. Description: Distributed Denial of Service attack detected from IP **********. Multiple sources overwhelming target with overwhelm. Impact: Service unavailability and resource exhaustion. Vulnerability: Insufficient rate limiting and traffic filtering.. CVSS Score: 5.3 (Medium). Metrics: AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N.",
    "Threat Type: DDoS Attack. Description: Distributed Denial of Service attack detected from IP **********. Multiple sources overwhelming target with flood. Impact: Service unavailability and resource exhaustion. Vulnerability: Insufficient rate limiting and traffic filtering.. CVSS Score: 5.3 (Medium). Metrics: AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N.",
    "Threat Type: DDoS Attack. Description: Distributed Denial of Service attack detected from IP **********. Multiple sources overwhelming target with unspecified method. Impact: Service unavailability and resource exhaustion. Vulnerability: Insufficient rate limiting and traffic filtering.. CVSS Score: 5.3 (Medium). Metrics: AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N."
  ],
  "processing_time": "2025-07-31T17:58:33.364355",
  "mode": "test"
}
```

---

## 关键差异总结 (Key Differences Summary)

### 单独运行 vs 协作工作流

| 方面 | 单独运行 | 协作工作流 |
|------|----------|------------|
| **数据输入** | 直接从原始数据文件读取 | 使用前一个Agent的输出 |
| **数据格式** | 严格按照原始文件格式 | 自动进行格式转换 |
| **执行方式** | 独立执行，手动指定参数 | 自动化流水线执行 |
| **结果输出** | 单个Agent的详细结果 | 完整工作流的聚合结果 |
| **错误处理** | Agent级别的错误处理 | 工作流级别的错误处理和回滚 |
| **性能评估** | 单个Agent的评估指标 | 端到端的系统性能评估 |

### 数据处理策略

1. **Summarization Agent**: 忽略真实标签，仅基于描述文本分类
2. **Prompt Agent**: 使用完整记录信息生成增强描述
3. **Comprehensive Decision Agent**: 忽略现有CVSS指标，重新计算
4. **Specific Advice Agent**: 忽略现有策略输出，基于输入重新生成

这种设计确保了每个Agent都能独立工作，同时在协作模式下能够无缝集成。

---

## 完整数据流转总结 (Complete Data Flow Summary)

### 原始数据到最终策略的转换过程

**原始输入** (来自 `output_0515_dataset_fin.json`):
```json
[false, "ground", "Network Traffic", "DDoS", "2024-04-13 09:30:17",
 "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [64231]\nDestination Port: [3690]\nProtocol: [TCP]\nMethod: [DDoS LOIT attack]\nDescription: A DDoS LOIT attack targeting Ubuntu16 was detected...",
 "Tag 1", 0]
```

**↓ Summarization Agent 处理**
- 提取索引5的描述文本
- 基于关键词规则分类
- 输出: `"DDoS"`

**↓ Prompt Agent 处理**
- 使用完整记录信息
- 生成结构化威胁描述
- 输出: `"Distributed Denial of Service attack detected from IP **********. Multiple sources overwhelming target with overwhelm. Impact: Service unavailability and resource exhaustion. Vulnerability: Insufficient rate limiting and traffic filtering."`

**↓ 数据格式转换**
- 转换为CVSS Agent输入格式
- 添加Category, Metrics, Base Score字段

**↓ Comprehensive Decision Agent 处理**
- 分析威胁描述
- 计算CVSS 3.1指标和评分
- 输出: `CVSS Score: 5.3, Metrics: AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N`

**↓ 数据格式转换**
- 转换为Strategy Agent输入格式
- 创建instruction-input-output结构

**↓ Specific Advice Agent 处理**
- 基于威胁类型和CVSS评分
- 选择合适的安全策略组合
- 最终输出: `["Use DNS filtering", "Conduct security awareness training", "Deploy web application firewall", "Update security patches regularly", "Implement rate limiting"]`

### 关键观察

1. **数据格式适配**: 每个Agent之间需要进行数据格式转换，确保输入格式匹配
2. **信息增强**: 每个Agent都在原有信息基础上增加新的分析结果
3. **端到端处理**: 从原始日志到最终安全策略建议的完整自动化流程
4. **模块化设计**: 每个Agent可以独立运行和测试，也可以组合成工作流
5. **一致性保证**: 测试模式下使用规则基础的方法，确保结果的可重现性

### ⚠️ 重要数据格式说明

**注意**: 上述演示中存在数据格式不一致的问题。实际的 `output_0525_finetune_metrics.json` 格式为：

```json
{
  "CVE ID": "CVE-2023-0001",
  "Time": "2024-01-02 03:04:05",
  "Description": "Origin IP: [**********]\nTarget IP: [*************]...",
  "Base Score": 7.4822427750000005,
  "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]
}
```

**正确的工作流应该**:
1. **Comprehensive Decision Agent** 应该直接处理 `output_0525_finetune_metrics.json` 格式
2. **数据转换** 应该将 Prompt Agent 的输出转换为上述正确格式
3. **Metrics字段** 应该是数组格式，不是空字符串
4. **Base Score** 应该有实际的数值，不是0.0

**修正后的数据转换应该是**:
```python
def transform_for_cvss(prompt_data):
    descriptions = prompt_data.get("generated_descriptions", [])
    categories = prompt_data.get("categories", [])

    cvss_input = []
    for i, desc in enumerate(descriptions):
        cvss_input.append({
            "CVE ID": f"CVE-2024-{1000+i:04d}",
            "Time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "Description": desc,
            "Base Score": 0.0,  # 待CVSS Agent计算
            "Metrics": []  # 待CVSS Agent填充
        })
    return cvss_input
```

### 修正后的工作流演示

**运行修正版本**:
```bash
python corrected_workflow_demo.py
```

**修正后的数据格式对比**:

1. **正确的CVSS输入格式**:
```json
{
  "CVE ID": "CVE-2024-1000",
  "Time": "2025-07-31 18:13:23",
  "Description": "Distributed Denial of Service attack detected from IP **********. Multiple sources overwhelming target with overwhelm. Impact: Service unavailability and resource exhaustion. Vulnerability: Insufficient rate limiting and traffic filtering.",
  "Base Score": 0.0,
  "Metrics": []
}
```

2. **之前错误的格式**:
```json
{
  "Description": "A DDoS LOIT attack has been detected...",
  "Category": "DDoS",
  "Metrics": "",
  "Base Score": 0.0
}
```

3. **实际的 output_0525_finetune_metrics.json 格式**:
```json
{
  "CVE ID": "CVE-2023-0001",
  "Time": "2024-01-02 03:04:05",
  "Description": "Origin IP: [**********]\\nTarget IP: [*************]...",
  "Base Score": 7.4822427750000005,
  "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]
}
```

**关键修正点**:
- ✅ 添加了 `CVE ID` 字段
- ✅ 添加了 `Time` 字段
- ✅ `Metrics` 改为数组格式，不是空字符串
- ✅ 移除了不存在的 `Category` 字段
- ✅ 保持 `Base Score` 和 `Description` 字段的正确格式

### Specific Advice Agent 格式问题

**Specific Advice Agent 也存在类似的格式不一致问题**:

**实际的 output_1112_strategy_train_data.json 格式**:
```json
{
  "instruction": "Please select the most appropriate set of strategies from the list below based on the description, and output them in strict list format directly(['xxx', 'xxx', 'xxxx',...]),please output only a list, not any extraneous words: [[\"Limit Hardware Installation\", \"Active Directory Configuration\", \"SSL/TLS Inspection\", \"Environment Variable Permissions\", \"Restrict Web-Based Content\"], [\"Disable or Remove Feature or Program\", \"Encrypt Sensitive Information\", \"Filter Network Traffic\", \"Limit Access to Resource Over Network\", \"Network Intrusion Prevention\", \"Network Segmentation\", \"User Training\"], [\"Active Directory Configuration\", \"User Training\", \"Application Developer Guidance\", \"Password Policies\", \"Filter Network Traffic\"], [\"Privileged Process Integrity\", \"Data Loss Prevention\", \"Privileged Account Management\", \"Pre-compromise\", \"Threat Intelligence Program\", \"Software Configuration\", \"Network Intrusion Prevention\"]] ,please just output the list alone.The description is following:",
  "input": "IP: [************]\\nDescription: Potential [eavesdropping] identified in satellite communication channels.\\nAttack Method: Exploitation of an [unencrypted data stream], allowing interception of transmitted data.\\nImpact: Threat of confidential information being accessed by unauthorized entities.\\nVulnerability: [Absence of encryption] on the frequencies used for data transmission.",
  "output": [
    "Disable or Remove Feature or Program",
    "Encrypt Sensitive Information",
    "Filter Network Traffic",
    "Limit Access to Resource Over Network",
    "Network Intrusion Prevention",
    "Network Segmentation",
    "User Training"
  ]
}
```

**我们在工作流中使用的格式**:
```json
{
  "instruction": "Generate appropriate security strategies for the following threat:",
  "input": "Threat Type: DDoS Attack. Description: Distributed Denial of Service attack detected from IP **********. Multiple sources overwhelming target with overwhelm. Impact: Service unavailability and resource exhaustion. Vulnerability: Insufficient rate limiting and traffic filtering.. CVSS Score: 5.3 (Medium). Metrics: AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N.",
  "output": []
}
```

**主要差异**:
1. **Instruction 内容**: 原始格式包含详细的策略选项列表和具体的输出格式要求
2. **Input 格式**: 原始格式使用 `IP: [xxx]\\nDescription: xxx\\nAttack Method: xxx\\nImpact: xxx\\nVulnerability: xxx` 的结构化格式
3. **策略选择逻辑**: 原始instruction提供了具体的策略组合选项，而我们使用的是开放式的策略生成

**修正建议**: 虽然当前格式在技术上是兼容的（Agent能够处理），但为了获得更好的策略选择效果，应该使用与训练数据一致的instruction和input格式。

### 实际应用价值

- **威胁分析自动化**: 从原始日志自动识别威胁类型
- **描述标准化**: 生成结构化的威胁描述，便于后续处理
- **风险量化**: 使用CVSS标准对威胁进行量化评估
- **策略推荐**: 基于威胁特征自动推荐相应的安全策略
- **可扩展性**: 支持添加新的Agent或修改现有Agent逻辑
