# Multi-Agent System Demo v2

A streamlined multi-agent system demo based on the LLM-SA framework for zero-trust network situation awareness.

## System Architecture

The system consists of four sequential agents:

1. **Summarization Agent** - Threat classification from raw logs
2. **Prompt Agent** - Description generation and data formatting  
3. **Comprehensive Decision Agent** - CVSS scoring and risk assessment
4. **Specific Advice Agent** - Security strategy generation

## Dual Operation Modes

### Test Mode (Default)
- Uses rule-based logic for all agents
- Fast execution for development and testing
- No external model dependencies

### LLM Mode  
- Uses large language models for agent decisions
- Higher quality results for final demonstrations
- Requires model configuration

## Dual Execution Methods

### Standalone Agent Execution
Run individual agents directly:
```bash
python "Summarization Agent.py" --help
python "Prompt Agent.py" --help  
python "Comprehensive Decision Agent.py" --help
python "Specific Advice Agent.py" --help
```

### Collaborative Workflow
Run all agents in sequence:
```bash
python collaborative_workflow.py --help
```

## Quick Start

### Interactive Demo
```bash
python demo_runner.py --interactive
```

### Run Specific Agent
```bash
# Test mode with limited records
python demo_runner.py --agent summarization --test-mode --max-records 10

# LLM mode (requires model)
python demo_runner.py --agent summarization --llm-mode --model-path /path/to/model
```

### Run Collaborative Workflow
```bash
# Test mode with limited records
python demo_runner.py --workflow --test-mode --max-records 5

# Full workflow
python demo_runner.py --workflow --test-mode
```

### Configuration Management
```bash
# View current configuration
python config.py --show

# Set test mode
python config.py --set-test-mode --save

# Set LLM mode with model
python config.py --set-llm-mode --model-path /path/to/model --save
```

## File Structure

```
agent_demo_v2/
├── Summarization Agent.py          # Agent 1: Threat classification
├── Prompt Agent.py                 # Agent 2: Description generation
├── Comprehensive Decision Agent.py # Agent 3: CVSS scoring
├── Specific Advice Agent.py        # Agent 4: Strategy generation
├── collaborative_workflow.py       # Workflow orchestrator
├── config.py                      # Configuration management
├── demo_runner.py                 # Main demo interface
└── README.md                      # This file
```

## Data Requirements

The system expects these data files in the parent directory:
- `output_0515_dataset_fin.json` - Raw threat logs
- `output_0525_finetune_metrics.json` - CVSS training data  
- `output_1112_strategy_train_data.json` - Strategy training data

## Agent Details

### Summarization Agent
- **Input**: Raw logs (ignores category field)
- **Output**: Predicted attack categories
- **Evaluation**: Classification accuracy
- **Test Mode**: Rule-based keyword matching

### Prompt Agent  
- **Input**: Complete records
- **Output**: Enhanced descriptions
- **Evaluation**: Manual quality assessment
- **Test Mode**: Template-based generation

### Comprehensive Decision Agent
- **Input**: Threat descriptions (ignores existing metrics)
- **Output**: CVSS metrics and base scores
- **Evaluation**: Metrics exact match rate, score MAE/RMSE
- **Test Mode**: Rule-based CVSS calculation

### Specific Advice Agent
- **Input**: Instruction and input fields
- **Output**: Strategy lists ['strategy1', 'strategy2', ...]
- **Evaluation**: Strategy selection accuracy
- **Test Mode**: Rule-based strategy mapping

## Example Usage

### Test Single Agent
```bash
cd agent_demo_v2
python "Summarization Agent.py" --input ../../output_0515_dataset_fin.json --max-records 10
```

### Test Workflow
```bash
cd agent_demo_v2  
python collaborative_workflow.py --max-records 5
```

### Interactive Demo
```bash
cd agent_demo_v2
python demo_runner.py
# Follow the interactive menu
```

## Output Files

Each agent and workflow run generates timestamped output files:
- `summarization_results_YYYYMMDD_HHMMSS.json`
- `prompt_agent_results_YYYYMMDD_HHMMSS.json`
- `comprehensive_decision_results_YYYYMMDD_HHMMSS.json`
- `specific_advice_results_YYYYMMDD_HHMMSS.json`
- `collaborative_workflow_results_YYYYMMDD_HHMMSS.json`

## Configuration Options

### Agent Parameters
- `--test-mode` / `--llm-mode`: Operation mode
- `--model-path`: Path to LLM model (LLM mode only)
- `--max-records`: Limit number of records processed
- `--show-samples`: Number of sample results to display

### Workflow Parameters  
- `--save-results`: Save workflow results to file
- All agent parameters apply to workflow execution

## Development Notes

- All code uses English comments and variable names
- Agents are designed to run independently
- Workflow handles data format conversion between agents
- Configuration system ensures consistent parameters across components
- Test mode provides fast execution for development

## Troubleshooting

### File Not Found Errors
Ensure data files exist in the correct relative paths:
```bash
ls -la ../../output_*.json
```

### Permission Errors
Make scripts executable:
```bash
chmod +x *.py
```

### Import Errors
Ensure you're running from the correct directory:
```bash
cd agent_demo_v2
python demo_runner.py
```
