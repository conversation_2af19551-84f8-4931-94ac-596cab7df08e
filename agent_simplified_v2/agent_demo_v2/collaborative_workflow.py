#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Collaborative Workflow Runner
Orchestrates the execution of all four agents in sequence

Workflow:
1. Summarization Agent: Raw logs -> Threat categories
2. Prompt Agent: Complete records -> Enhanced descriptions  
3. Comprehensive Decision Agent: Descriptions -> CVSS scores
4. Specific Advice Agent: CVSS results -> Security strategies

Supports both test mode and LLM mode execution
"""

import json
import os
import sys
import argparse
import subprocess
from typing import Dict, Any, List
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class CollaborativeWorkflow:
    """Main workflow orchestrator"""
    
    def __init__(self, test_mode: bool = True, model_path: str = None, max_records: int = None):
        self.test_mode = test_mode
        self.model_path = model_path
        self.max_records = max_records
        self.workflow_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Agent file paths
        self.agents = {
            'summarization': 'Summarization Agent.py',
            'prompt': 'Prompt Agent.py', 
            'comprehensive': 'Comprehensive Decision Agent.py',
            'specific': 'Specific Advice Agent.py'
        }
        
        # Data file paths (relative to parent directory)
        self.data_files = {
            'raw_logs': '../../output_0515_dataset_fin.json',
            'cvss_data': '../../output_0525_finetune_metrics.json',
            'strategy_data': '../../output_1112_strategy_train_data.json'
        }
        
        logger.info(f"Workflow initialized - ID: {self.workflow_id}, Mode: {'Test' if test_mode else 'LLM'}")
    
    def run_agent(self, agent_name: str, input_file: str, output_file: str, additional_args: List[str] = None) -> Dict[str, Any]:
        """
        Run a single agent with specified parameters
        
        Args:
            agent_name: Name of the agent to run
            input_file: Input data file path
            output_file: Output results file path
            additional_args: Additional command line arguments
            
        Returns:
            Agent execution results
        """
        if agent_name not in self.agents:
            raise ValueError(f"Unknown agent: {agent_name}")
        
        agent_script = self.agents[agent_name]
        
        # Build command
        cmd = ['python', agent_script, '--input', input_file, '--output', output_file]
        
        # Add mode selection
        if self.test_mode:
            cmd.append('--test-mode')
        else:
            cmd.extend(['--llm-mode', '--model-path', self.model_path or ''])
        
        # Add max records limit if specified
        if self.max_records:
            cmd.extend(['--max-records', str(self.max_records)])
        
        # Add additional arguments
        if additional_args:
            cmd.extend(additional_args)
        
        logger.info(f"Running {agent_name} agent: {' '.join(cmd)}")
        
        try:
            # Run the agent
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            # Load results from output file
            if os.path.exists(output_file):
                with open(output_file, 'r', encoding='utf-8') as f:
                    agent_results = json.load(f)
            else:
                agent_results = {}
            
            return {
                'success': True,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'results': agent_results
            }
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Agent {agent_name} failed: {e}")
            return {
                'success': False,
                'stdout': e.stdout,
                'stderr': e.stderr,
                'error': str(e)
            }
    
    def create_intermediate_data(self, agent_results: Dict[str, Any], data_type: str) -> str:
        """
        Create intermediate data file for next agent in pipeline
        
        Args:
            agent_results: Results from previous agent
            data_type: Type of data to create ('descriptions', 'cvss_inputs', etc.)
            
        Returns:
            Path to created intermediate data file
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"intermediate_{data_type}_{timestamp}.json"
        
        if data_type == 'descriptions':
            # Create data for Comprehensive Decision Agent
            # Convert Prompt Agent results to CVSS input format
            descriptions = agent_results.get('generated_descriptions', [])
            categories = agent_results.get('categories', [])
            
            intermediate_data = []
            for i, (desc, cat) in enumerate(zip(descriptions, categories)):
                intermediate_data.append({
                    'Description': desc,
                    'Category': cat,
                    'Metrics': '',  # Will be filled by Comprehensive Decision Agent
                    'Base Score': 0.0  # Will be filled by Comprehensive Decision Agent
                })
        
        elif data_type == 'cvss_inputs':
            # Create data for Specific Advice Agent
            # Convert Comprehensive Decision results to strategy input format
            strategy_inputs = agent_results.get('strategy_inputs', [])
            
            intermediate_data = []
            for i, strategy_input in enumerate(strategy_inputs):
                intermediate_data.append({
                    'instruction': 'Generate appropriate security strategies for the following threat:',
                    'input': strategy_input,
                    'output': []  # Will be filled by Specific Advice Agent
                })
        
        else:
            intermediate_data = agent_results
        
        # Save intermediate data
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(intermediate_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Created intermediate data file: {filename}")
        return filename
    
    def run_complete_workflow(self) -> Dict[str, Any]:
        """
        Run the complete collaborative workflow
        
        Returns:
            Complete workflow results
        """
        workflow_results = {
            'workflow_id': self.workflow_id,
            'start_time': datetime.now().isoformat(),
            'mode': 'test' if self.test_mode else 'llm',
            'max_records': self.max_records,
            'agents': {}
        }
        
        try:
            # Step 1: Summarization Agent
            logger.info("="*60)
            logger.info("STEP 1: Running Summarization Agent")
            logger.info("="*60)
            
            summarization_output = f"summarization_results_{self.workflow_id}.json"
            summarization_result = self.run_agent(
                'summarization',
                self.data_files['raw_logs'],
                summarization_output
            )
            workflow_results['agents']['summarization'] = summarization_result
            
            if not summarization_result['success']:
                raise Exception("Summarization Agent failed")
            
            # Step 2: Prompt Agent
            logger.info("="*60)
            logger.info("STEP 2: Running Prompt Agent")
            logger.info("="*60)
            
            prompt_output = f"prompt_results_{self.workflow_id}.json"
            prompt_result = self.run_agent(
                'prompt',
                self.data_files['raw_logs'],  # Uses same raw data
                prompt_output
            )
            workflow_results['agents']['prompt'] = prompt_result
            
            if not prompt_result['success']:
                raise Exception("Prompt Agent failed")
            
            # Create intermediate data for Comprehensive Decision Agent
            cvss_input_file = self.create_intermediate_data(prompt_result['results'], 'descriptions')
            
            # Step 3: Comprehensive Decision Agent
            logger.info("="*60)
            logger.info("STEP 3: Running Comprehensive Decision Agent")
            logger.info("="*60)
            
            comprehensive_output = f"comprehensive_results_{self.workflow_id}.json"
            comprehensive_result = self.run_agent(
                'comprehensive',
                cvss_input_file,
                comprehensive_output
            )
            workflow_results['agents']['comprehensive'] = comprehensive_result
            
            if not comprehensive_result['success']:
                raise Exception("Comprehensive Decision Agent failed")
            
            # Create intermediate data for Specific Advice Agent
            strategy_input_file = self.create_intermediate_data(comprehensive_result['results'], 'cvss_inputs')
            
            # Step 4: Specific Advice Agent
            logger.info("="*60)
            logger.info("STEP 4: Running Specific Advice Agent")
            logger.info("="*60)
            
            specific_output = f"specific_results_{self.workflow_id}.json"
            specific_result = self.run_agent(
                'specific',
                strategy_input_file,
                specific_output
            )
            workflow_results['agents']['specific'] = specific_result
            
            if not specific_result['success']:
                raise Exception("Specific Advice Agent failed")
            
            workflow_results['success'] = True
            workflow_results['end_time'] = datetime.now().isoformat()
            
            logger.info("="*60)
            logger.info("WORKFLOW COMPLETED SUCCESSFULLY")
            logger.info("="*60)
            
        except Exception as e:
            workflow_results['success'] = False
            workflow_results['error'] = str(e)
            workflow_results['end_time'] = datetime.now().isoformat()
            logger.error(f"Workflow failed: {e}")
        
        return workflow_results
    
    def save_workflow_results(self, results: Dict[str, Any]) -> str:
        """
        Save complete workflow results
        
        Args:
            results: Complete workflow results
            
        Returns:
            Path to saved results file
        """
        output_file = f"collaborative_workflow_results_{self.workflow_id}.json"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Workflow results saved to: {output_file}")
        return output_file
    
    def display_workflow_summary(self, results: Dict[str, Any]):
        """
        Display workflow execution summary
        
        Args:
            results: Complete workflow results
        """
        print("\n" + "="*80)
        print("COLLABORATIVE WORKFLOW SUMMARY")
        print("="*80)
        print(f"Workflow ID: {results['workflow_id']}")
        print(f"Mode: {results['mode'].upper()}")
        print(f"Max Records: {results.get('max_records', 'All')}")
        print(f"Success: {results['success']}")
        print(f"Start Time: {results['start_time']}")
        print(f"End Time: {results['end_time']}")
        
        if results['success']:
            print("\nAgent Results:")
            for agent_name, agent_result in results['agents'].items():
                print(f"  {agent_name.title()}: {'✓ Success' if agent_result['success'] else '✗ Failed'}")
                if 'results' in agent_result and 'total_records' in agent_result['results']:
                    print(f"    Records processed: {agent_result['results']['total_records']}")
        else:
            print(f"\nError: {results.get('error', 'Unknown error')}")
        
        print("="*80)


def main():
    """Main function for workflow execution"""
    parser = argparse.ArgumentParser(description='Collaborative Multi-Agent Workflow')
    parser.add_argument('--test-mode', action='store_true', default=True,
                       help='Use test mode (rule-based agents)')
    parser.add_argument('--llm-mode', action='store_true',
                       help='Use LLM mode')
    parser.add_argument('--model-path', default=None,
                       help='Path to LLM model (for LLM mode)')
    parser.add_argument('--max-records', type=int, default=None,
                       help='Maximum number of records to process per agent')
    parser.add_argument('--save-results', action='store_true', default=True,
                       help='Save workflow results to file')
    
    args = parser.parse_args()
    
    # Determine mode
    test_mode = not args.llm_mode if args.llm_mode else args.test_mode
    
    print("="*80)
    print("COLLABORATIVE MULTI-AGENT WORKFLOW")
    print("="*80)
    print(f"Mode: {'Test (Rule-based)' if test_mode else 'LLM'}")
    print(f"Max records per agent: {args.max_records or 'All'}")
    print(f"Model path: {args.model_path if not test_mode else 'N/A'}")
    print()
    
    try:
        # Initialize workflow
        workflow = CollaborativeWorkflow(
            test_mode=test_mode,
            model_path=args.model_path,
            max_records=args.max_records
        )
        
        # Run complete workflow
        results = workflow.run_complete_workflow()
        
        # Save results if requested
        if args.save_results:
            workflow.save_workflow_results(results)
        
        # Display summary
        workflow.display_workflow_summary(results)
        
        # Exit with appropriate code
        sys.exit(0 if results['success'] else 1)
        
    except Exception as e:
        logger.error(f"Workflow execution failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
