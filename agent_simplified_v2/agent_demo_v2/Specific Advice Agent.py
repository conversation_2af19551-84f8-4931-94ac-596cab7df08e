#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Specific Advice Agent - Strategy Generation
Standalone executable agent for strategy generation task

Core functionality:
- Input: instruction and input fields from output_1112_strategy_train_data.json
- Output: Strategy list in format ['strategy1', 'strategy2', ...]
- Evaluation: Calculate strategy selection accuracy
- Implementation: Select most appropriate strategies from predefined options
"""

import json
import os
import sys
import argparse
from typing import List, Dict, Any, Tuple
from datetime import datetime
import logging
import re

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class StrategySelector:
    """Rule-based strategy selector for test mode"""
    
    def __init__(self):
        # Predefined strategy options based on common cybersecurity practices
        self.available_strategies = [
            "Implement network segmentation",
            "Deploy intrusion detection systems",
            "Enable multi-factor authentication",
            "Update security patches regularly",
            "Conduct security awareness training",
            "Implement access control policies",
            "Deploy endpoint protection",
            "Monitor network traffic",
            "Backup data regularly",
            "Implement incident response plan",
            "Use encryption for sensitive data",
            "Deploy web application firewall",
            "Implement rate limiting",
            "Use threat intelligence feeds",
            "Deploy SIEM solutions",
            "Conduct vulnerability assessments",
            "Implement zero-trust architecture",
            "Use secure coding practices",
            "Deploy honeypots and deception technology",
            "Implement behavioral analysis",
            "Use DNS filtering",
            "Deploy anti-malware solutions",
            "Implement privilege escalation controls",
            "Use secure communication protocols",
            "Deploy network access control",
            "Implement data loss prevention",
            "Use security orchestration tools",
            "Deploy container security",
            "Implement cloud security controls",
            "Use identity and access management"
        ]
        
        # Strategy mapping based on threat types and CVSS scores
        self.threat_strategy_map = {
            'ddos': [
                "Implement rate limiting",
                "Deploy web application firewall", 
                "Use DNS filtering",
                "Monitor network traffic",
                "Implement network segmentation"
            ],
            'dos': [
                "Implement rate limiting",
                "Deploy endpoint protection",
                "Monitor network traffic",
                "Implement access control policies"
            ],
            'infiltration': [
                "Enable multi-factor authentication",
                "Implement access control policies",
                "Deploy intrusion detection systems",
                "Implement zero-trust architecture",
                "Use identity and access management"
            ],
            'brute force': [
                "Enable multi-factor authentication",
                "Implement access control policies",
                "Deploy intrusion detection systems",
                "Implement privilege escalation controls",
                "Monitor network traffic"
            ],
            'web attack': [
                "Deploy web application firewall",
                "Use secure coding practices",
                "Implement input validation",
                "Deploy endpoint protection",
                "Conduct vulnerability assessments"
            ],
            'port scan': [
                "Implement network segmentation",
                "Deploy intrusion detection systems",
                "Monitor network traffic",
                "Implement access control policies",
                "Use threat intelligence feeds"
            ],
            'botnet': [
                "Deploy anti-malware solutions",
                "Deploy endpoint protection",
                "Monitor network traffic",
                "Implement behavioral analysis",
                "Use threat intelligence feeds"
            ]
        }
        
        # Severity-based strategy priorities
        self.severity_strategies = {
            'critical': [
                "Implement incident response plan",
                "Deploy SIEM solutions",
                "Implement zero-trust architecture",
                "Use security orchestration tools",
                "Conduct vulnerability assessments"
            ],
            'high': [
                "Deploy intrusion detection systems",
                "Implement network segmentation",
                "Enable multi-factor authentication",
                "Deploy endpoint protection",
                "Monitor network traffic"
            ],
            'medium': [
                "Update security patches regularly",
                "Implement access control policies",
                "Conduct security awareness training",
                "Deploy web application firewall",
                "Use encryption for sensitive data"
            ],
            'low': [
                "Conduct security awareness training",
                "Update security patches regularly",
                "Backup data regularly",
                "Monitor network traffic",
                "Implement access control policies"
            ]
        }
    
    def analyze_input(self, instruction: str, input_text: str) -> Dict[str, Any]:
        """
        Analyze instruction and input to extract key information
        
        Args:
            instruction: Instruction text
            input_text: Input text with threat information
            
        Returns:
            Dictionary with extracted information
        """
        combined_text = f"{instruction} {input_text}".lower()
        
        # Extract threat type
        threat_type = 'unknown'
        for threat in ['ddos', 'dos', 'infiltration', 'brute force', 'web attack', 'port scan', 'botnet']:
            if threat in combined_text:
                threat_type = threat
                break
        
        # Extract severity level
        severity = 'medium'  # default
        if 'critical' in combined_text or 'score: 9' in combined_text or 'score: 10' in combined_text:
            severity = 'critical'
        elif 'high' in combined_text or 'score: 7' in combined_text or 'score: 8' in combined_text:
            severity = 'high'
        elif 'low' in combined_text or 'score: 1' in combined_text or 'score: 2' in combined_text or 'score: 3' in combined_text:
            severity = 'low'
        
        # Extract CVSS score if available
        cvss_score = None
        score_match = re.search(r'score[:\s]+(\d+\.?\d*)', combined_text)
        if score_match:
            cvss_score = float(score_match.group(1))
        
        # Extract specific vulnerabilities or attack methods
        vulnerabilities = []
        vuln_keywords = ['sql injection', 'xss', 'buffer overflow', 'privilege escalation', 
                        'code injection', 'authentication bypass', 'directory traversal']
        for vuln in vuln_keywords:
            if vuln in combined_text:
                vulnerabilities.append(vuln)
        
        return {
            'threat_type': threat_type,
            'severity': severity,
            'cvss_score': cvss_score,
            'vulnerabilities': vulnerabilities,
            'combined_text': combined_text
        }
    
    def select_strategies(self, instruction: str, input_text: str, max_strategies: int = 5) -> List[str]:
        """
        Select appropriate strategies based on instruction and input
        
        Args:
            instruction: Instruction text
            input_text: Input text with threat information
            max_strategies: Maximum number of strategies to return
            
        Returns:
            List of selected strategies
        """
        analysis = self.analyze_input(instruction, input_text)
        
        selected_strategies = set()
        
        # Add threat-specific strategies
        threat_type = analysis['threat_type']
        if threat_type in self.threat_strategy_map:
            selected_strategies.update(self.threat_strategy_map[threat_type][:3])
        
        # Add severity-based strategies
        severity = analysis['severity']
        if severity in self.severity_strategies:
            selected_strategies.update(self.severity_strategies[severity][:2])
        
        # Add vulnerability-specific strategies
        for vuln in analysis['vulnerabilities']:
            if 'sql injection' in vuln or 'xss' in vuln:
                selected_strategies.add("Deploy web application firewall")
                selected_strategies.add("Use secure coding practices")
            elif 'privilege escalation' in vuln:
                selected_strategies.add("Implement privilege escalation controls")
                selected_strategies.add("Enable multi-factor authentication")
        
        # Ensure we have enough strategies
        if len(selected_strategies) < max_strategies:
            # Add general security strategies
            general_strategies = [
                "Update security patches regularly",
                "Conduct security awareness training", 
                "Backup data regularly",
                "Implement incident response plan",
                "Monitor network traffic"
            ]
            for strategy in general_strategies:
                if strategy not in selected_strategies:
                    selected_strategies.add(strategy)
                    if len(selected_strategies) >= max_strategies:
                        break
        
        # Convert to list and limit to max_strategies
        strategy_list = list(selected_strategies)[:max_strategies]
        
        # If still not enough, pad with available strategies
        while len(strategy_list) < min(max_strategies, 3):
            for strategy in self.available_strategies:
                if strategy not in strategy_list:
                    strategy_list.append(strategy)
                    break
        
        return strategy_list


class LLMStrategySelector:
    """Mock LLM interface for strategy selection"""
    
    def __init__(self, model_path: str = None):
        self.model_path = model_path
        logger.info(f"LLM Strategy Selector initialized with model: {model_path}")
    
    def select_strategies(self, instruction: str, input_text: str, max_strategies: int = 5) -> List[str]:
        """
        Mock LLM strategy selection - in real implementation, this would call actual LLM
        
        Args:
            instruction: Instruction text
            input_text: Input text with threat information
            max_strategies: Maximum number of strategies to return
            
        Returns:
            List of selected strategies
        """
        # For demo purposes, use rule-based selection
        # In real implementation, this would call the LLM model
        selector = StrategySelector()
        return selector.select_strategies(instruction, input_text, max_strategies)


class SpecificAdviceAgent:
    """Main Specific Advice Agent class"""
    
    def __init__(self, test_mode: bool = True, model_path: str = None):
        self.test_mode = test_mode
        self.model_path = model_path
        
        if test_mode:
            self.selector = StrategySelector()
            logger.info("Initialized in test mode with rule-based strategy selector")
        else:
            self.selector = LLMStrategySelector(model_path)
            logger.info("Initialized in LLM mode")
    
    def load_data(self, file_path: str) -> List[Dict[str, Any]]:
        """
        Load strategy data from JSON file
        
        Args:
            file_path: Path to the data file
            
        Returns:
            List of strategy records
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Data file not found: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        logger.info(f"Loaded {len(data)} records from {file_path}")
        return data
    
    def extract_instruction(self, record: Dict[str, Any]) -> str:
        """
        Extract instruction from strategy record
        
        Args:
            record: Single record from the dataset
            
        Returns:
            Instruction text
        """
        return record.get('instruction', '')
    
    def extract_input(self, record: Dict[str, Any]) -> str:
        """
        Extract input from strategy record
        
        Args:
            record: Single record from the dataset
            
        Returns:
            Input text
        """
        return record.get('input', '')
    
    def get_true_strategies(self, record: Dict[str, Any]) -> List[str]:
        """
        Extract true strategies from record (for evaluation)
        
        Args:
            record: Single record from the dataset
            
        Returns:
            List of true strategies
        """
        output = record.get('output', [])
        if isinstance(output, list):
            return output
        elif isinstance(output, str):
            # Try to parse as JSON list
            try:
                return json.loads(output)
            except:
                return [output]
        else:
            return []
    
    def generate_strategies(self, instruction: str, input_text: str, max_strategies: int = 5) -> List[str]:
        """
        Generate strategies for a single instruction/input pair
        
        Args:
            instruction: Instruction text
            input_text: Input text
            max_strategies: Maximum number of strategies to return
            
        Returns:
            List of generated strategies
        """
        return self.selector.select_strategies(instruction, input_text, max_strategies)
    
    def process_dataset(self, file_path: str, max_records: int = None) -> Dict[str, Any]:
        """
        Process entire dataset and return results
        
        Args:
            file_path: Path to input data file
            max_records: Maximum number of records to process (for testing)
            
        Returns:
            Processing results with generated strategies
        """
        # Load data
        data = self.load_data(file_path)
        
        if max_records:
            data = data[:max_records]
            logger.info(f"Processing limited to {max_records} records")
        
        results = {
            'total_records': len(data),
            'predicted_strategies': [],
            'true_strategies': [],
            'instructions': [],
            'inputs': [],
            'processing_time': datetime.now().isoformat(),
            'mode': 'test' if self.test_mode else 'llm'
        }
        
        # Process each record
        for i, record in enumerate(data):
            instruction = self.extract_instruction(record)
            input_text = self.extract_input(record)
            true_strategies = self.get_true_strategies(record)
            
            # Generate strategies
            predicted_strategies = self.generate_strategies(instruction, input_text)
            
            results['predicted_strategies'].append(predicted_strategies)
            results['true_strategies'].append(true_strategies)
            results['instructions'].append(instruction)
            results['inputs'].append(input_text)
            
            if (i + 1) % 50 == 0:
                logger.info(f"Processed {i + 1}/{len(data)} records")
        
        # Calculate evaluation metrics
        accuracy_metrics = self.calculate_strategy_accuracy(results['true_strategies'], results['predicted_strategies'])
        results.update(accuracy_metrics)
        
        logger.info(f"Processing completed. Strategy accuracy: {accuracy_metrics.get('exact_match_rate', 0):.2%}")
        return results
    
    def calculate_strategy_accuracy(self, true_strategies: List[List[str]], predicted_strategies: List[List[str]]) -> Dict[str, float]:
        """
        Calculate accuracy metrics for strategy selection
        
        Args:
            true_strategies: List of true strategy lists
            predicted_strategies: List of predicted strategy lists
            
        Returns:
            Dictionary with accuracy metrics
        """
        if len(true_strategies) != len(predicted_strategies):
            raise ValueError("Length mismatch between true and predicted strategies")
        
        # Exact match rate
        exact_matches = 0
        partial_matches = 0
        total_precision = 0
        total_recall = 0
        
        for true_list, pred_list in zip(true_strategies, predicted_strategies):
            # Convert to sets for comparison
            true_set = set(true_list) if true_list else set()
            pred_set = set(pred_list) if pred_list else set()
            
            # Exact match
            if true_set == pred_set:
                exact_matches += 1
            
            # Partial match (any overlap)
            if true_set & pred_set:
                partial_matches += 1
            
            # Precision and Recall
            if pred_set:
                precision = len(true_set & pred_set) / len(pred_set)
                total_precision += precision
            
            if true_set:
                recall = len(true_set & pred_set) / len(true_set)
                total_recall += recall
        
        n = len(true_strategies)
        exact_match_rate = exact_matches / n if n > 0 else 0
        partial_match_rate = partial_matches / n if n > 0 else 0
        avg_precision = total_precision / n if n > 0 else 0
        avg_recall = total_recall / n if n > 0 else 0
        f1_score = 2 * (avg_precision * avg_recall) / (avg_precision + avg_recall) if (avg_precision + avg_recall) > 0 else 0
        
        return {
            'exact_match_rate': exact_match_rate,
            'partial_match_rate': partial_match_rate,
            'avg_precision': avg_precision,
            'avg_recall': avg_recall,
            'f1_score': f1_score
        }
    
    def save_results(self, results: Dict[str, Any], output_path: str = None) -> str:
        """
        Save results to JSON file
        
        Args:
            results: Processing results
            output_path: Output file path
            
        Returns:
            Path to saved file
        """
        if output_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"specific_advice_results_{timestamp}.json"
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Results saved to: {output_path}")
        return output_path
    
    def display_sample_results(self, results: Dict[str, Any], num_samples: int = 3):
        """
        Display sample results for manual quality assessment
        
        Args:
            results: Processing results
            num_samples: Number of samples to display
        """
        print("\n" + "="*80)
        print("SAMPLE STRATEGY GENERATION RESULTS")
        print("="*80)
        
        for i in range(min(num_samples, len(results['predicted_strategies']))):
            print(f"\nSample {i+1}:")
            print(f"Instruction: {results['instructions'][i][:100]}...")
            print(f"Input: {results['inputs'][i][:100]}...")
            print(f"True Strategies: {results['true_strategies'][i]}")
            print(f"Predicted Strategies: {results['predicted_strategies'][i]}")
            print("-" * 80)


def main():
    """Main function for standalone execution"""
    parser = argparse.ArgumentParser(description='Specific Advice Agent - Strategy Generation')
    parser.add_argument('--input', default='../../output_1112_strategy_train_data.json', 
                       help='Input data file path')
    parser.add_argument('--output', default=None, 
                       help='Output results file path')
    parser.add_argument('--max-records', type=int, default=None,
                       help='Maximum number of records to process')
    parser.add_argument('--max-strategies', type=int, default=5,
                       help='Maximum number of strategies to generate per record')
    parser.add_argument('--test-mode', action='store_true', default=True,
                       help='Use test mode (rule-based strategy selection)')
    parser.add_argument('--llm-mode', action='store_true', 
                       help='Use LLM mode')
    parser.add_argument('--model-path', default=None,
                       help='Path to LLM model (for LLM mode)')
    parser.add_argument('--show-samples', type=int, default=3,
                       help='Number of sample results to display')
    
    args = parser.parse_args()
    
    # Determine mode
    test_mode = not args.llm_mode if args.llm_mode else args.test_mode
    
    print("="*60)
    print("Specific Advice Agent - Strategy Generation")
    print("="*60)
    print(f"Mode: {'Test (Rule-based)' if test_mode else 'LLM'}")
    print(f"Input file: {args.input}")
    print(f"Max records: {args.max_records or 'All'}")
    print(f"Max strategies per record: {args.max_strategies}")
    print(f"Model path: {args.model_path if not test_mode else 'N/A'}")
    print()
    
    try:
        # Initialize agent
        agent = SpecificAdviceAgent(test_mode=test_mode, model_path=args.model_path)
        
        # Process dataset
        results = agent.process_dataset(args.input, args.max_records)
        
        # Save results
        output_path = agent.save_results(results, args.output)
        
        # Display sample results
        if args.show_samples > 0:
            agent.display_sample_results(results, args.show_samples)
        
        # Display summary
        print("\n" + "="*60)
        print("PROCESSING SUMMARY")
        print("="*60)
        print(f"Total records processed: {results['total_records']}")
        print(f"Exact match rate: {results['exact_match_rate']:.2%}")
        print(f"Partial match rate: {results['partial_match_rate']:.2%}")
        print(f"Average precision: {results['avg_precision']:.2%}")
        print(f"Average recall: {results['avg_recall']:.2%}")
        print(f"F1 score: {results['f1_score']:.2%}")
        print(f"Results saved to: {output_path}")
        print("="*60)
        
    except Exception as e:
        logger.error(f"Error during processing: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
