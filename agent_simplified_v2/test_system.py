#!/usr/bin/env python3
"""
SAGIN Multi-Agent System 测试脚本
用于测试系统的各种功能和运行模式
"""

import sys
import json
from pathlib import Path
from typing import Dict, Any

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from main import SAGINMultiAgentSystem
from core import setup_logger

logger = setup_logger(__name__)


def test_system_initialization():
    """测试系统初始化"""
    print("\n🔧 测试系统初始化...")
    
    try:
        system = SAGINMultiAgentSystem()
        info = system.get_system_info()
        
        print(f"✅ 系统初始化成功")
        print(f"   版本: {info['version']}")
        print(f"   模式: {info['config_summary']['mode']}")
        print(f"   可用Agent: {len(info['available_agents'])} 个")
        
        return True
    except Exception as e:
        print(f"❌ 系统初始化失败: {e}")
        return False


def test_individual_agents():
    """测试独立Agent运行"""
    print("\n🤖 测试独立Agent运行...")
    
    system = SAGINMultiAgentSystem()
    input_file = "data/input/sample_threat_data.json"
    
    # 确保输入文件存在
    if not Path(input_file).exists():
        print(f"❌ 输入文件不存在: {input_file}")
        return False
    
    agents_to_test = ['summarization', 'prompt', 'specific_advice', 'comprehensive_decision']
    results = {}
    
    for agent_name in agents_to_test:
        print(f"  测试 {agent_name} Agent...")
        
        try:
            result = system.run_independent_agent(
                agent_name, 
                input_file, 
                f"data/output/test_{agent_name}_output.json"
            )
            
            if result['success']:
                print(f"  ✅ {agent_name} Agent 测试成功")
                results[agent_name] = True
            else:
                print(f"  ❌ {agent_name} Agent 测试失败: {result['error']}")
                results[agent_name] = False
                
        except Exception as e:
            print(f"  ❌ {agent_name} Agent 测试异常: {e}")
            results[agent_name] = False
    
    success_count = sum(1 for success in results.values() if success)
    print(f"\n📊 独立Agent测试结果: {success_count}/{len(agents_to_test)} 成功")
    
    return success_count == len(agents_to_test)


def test_collaborative_workflow():
    """测试协作工作流"""
    print("\n🔄 测试协作工作流...")
    
    system = SAGINMultiAgentSystem()
    input_file = "data/input/sample_threat_data.json"
    output_dir = "data/output/workflow_test"
    
    # 确保输入文件存在
    if not Path(input_file).exists():
        print(f"❌ 输入文件不存在: {input_file}")
        return False
    
    try:
        result = system.run_collaborative_workflow(input_file, output_dir)
        
        if result['success']:
            print(f"✅ 协作工作流测试成功")
            print(f"   输出目录: {result['output_directory']}")
            print(f"   执行的Agent: {', '.join(result['agents_executed'])}")
            print(f"   最终输出: {result['final_output']}")
            
            # 检查输出文件是否存在
            output_path = Path(result['output_directory'])
            expected_files = [
                "01_summarization_output.json",
                "02_prompt_output.json", 
                "03_specific_advice_output.json",
                "04_comprehensive_decision_output.json",
                "workflow_summary.json"
            ]
            
            missing_files = []
            for file_name in expected_files:
                if not (output_path / file_name).exists():
                    missing_files.append(file_name)
            
            if missing_files:
                print(f"⚠️  缺少输出文件: {', '.join(missing_files)}")
            else:
                print(f"✅ 所有输出文件生成成功")
            
            return True
        else:
            print(f"❌ 协作工作流测试失败: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ 协作工作流测试异常: {e}")
        return False


def test_configuration_loading():
    """测试配置加载"""
    print("\n⚙️  测试配置加载...")
    
    try:
        # 测试默认配置
        system1 = SAGINMultiAgentSystem()
        config1 = system1.config
        
        print(f"✅ 默认配置加载成功")
        print(f"   测试模式: {config1.test_mode}")
        print(f"   LLM类型: {config1.model.llm_type}")
        
        # 测试自定义配置文件（如果存在）
        custom_config_path = "config/custom_config.yaml"
        if Path(custom_config_path).exists():
            system2 = SAGINMultiAgentSystem(custom_config_path)
            print(f"✅ 自定义配置加载成功")
        else:
            print(f"ℹ️  自定义配置文件不存在，跳过测试")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置加载测试失败: {e}")
        return False


def test_data_processing():
    """测试数据处理功能"""
    print("\n📊 测试数据处理功能...")
    
    try:
        from core import DataLoader, ThreatClassifier, TextUtils
        
        # 测试数据加载
        data_loader = DataLoader()
        input_file = "data/input/sample_threat_data.json"
        
        if Path(input_file).exists():
            data = data_loader.load_json_data(input_file)
            print(f"✅ 数据加载成功，加载 {len(data)} 条记录")
        else:
            print(f"⚠️  测试数据文件不存在: {input_file}")
            return False
        
        # 测试威胁分类
        threat_classifier = ThreatClassifier()
        test_description = "Distributed denial of service attack detected"
        threat_type = threat_classifier.classify_threat_from_description(test_description)
        print(f"✅ 威胁分类测试成功，分类结果: {threat_type}")
        
        # 测试文本处理
        text_utils = TextUtils()
        text1 = "DDoS attack detected"
        text2 = "Denial of service attack identified"
        similarity = text_utils.calculate_text_similarity(text1, text2)
        print(f"✅ 文本相似度计算成功，相似度: {similarity:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据处理功能测试失败: {e}")
        return False


def generate_test_report(test_results: Dict[str, bool]):
    """生成测试报告"""
    print("\n" + "="*60)
    print("📋 SAGIN多Agent系统测试报告")
    print("="*60)
    
    total_tests = len(test_results)
    passed_tests = sum(1 for result in test_results.values() if result)
    
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"通过率: {passed_tests/total_tests*100:.1f}%")
    
    print("\n详细结果:")
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name:<25} {status}")
    
    print("\n" + "="*60)
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！系统运行正常。")
        return True
    else:
        print("⚠️  部分测试失败，请检查系统配置和依赖。")
        return False


def main():
    """主测试函数"""
    print("🚀 开始SAGIN多Agent系统测试...")
    
    # 确保必要的目录存在
    Path("data/output").mkdir(parents=True, exist_ok=True)
    Path("data/output/workflow_test").mkdir(parents=True, exist_ok=True)
    
    # 执行各项测试
    test_results = {}
    
    test_results["系统初始化"] = test_system_initialization()
    test_results["配置加载"] = test_configuration_loading()
    test_results["数据处理功能"] = test_data_processing()
    test_results["独立Agent运行"] = test_individual_agents()
    test_results["协作工作流"] = test_collaborative_workflow()
    
    # 生成测试报告
    all_passed = generate_test_report(test_results)
    
    if all_passed:
        print("\n🎯 测试建议:")
        print("  1. 系统已准备就绪，可以开始使用")
        print("  2. 尝试运行: python main.py workflow --input data/input/sample_threat_data.json")
        print("  3. 查看输出结果并验证分析质量")
    else:
        print("\n🔧 故障排除建议:")
        print("  1. 检查Python依赖是否完整安装")
        print("  2. 确认配置文件格式正确")
        print("  3. 验证输入数据文件存在且格式正确")
        print("  4. 查看日志文件获取详细错误信息")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
