#!/usr/bin/env python3
"""
SAGIN Multi-Agent System 主运行脚本
支持协作工作流和独立Agent运行
"""

import sys
import argparse
from pathlib import Path
from typing import Optional, Dict, Any

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from core import setup_logger
from core.config_manager import setup_global_config
from agents.summarization_agent import SummarizationAgent
from agents.prompt_agent import PromptAgent
from agents.specific_advice_agent import SpecificAdviceAgent
from agents.comprehensive_decision_agent import ComprehensiveDecisionAgent

logger = setup_logger(__name__)


class SAGINMultiAgentSystem:
    """SAGIN多Agent系统主类"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化多Agent系统
        
        Args:
            config_path: 配置文件路径
        """
        # 设置全局配置
        self.config = setup_global_config(config_path)
        
        # 初始化所有Agent
        self.agents = {
            'summarization': SummarizationAgent(self.config),
            'prompt': PromptAgent(self.config),
            'specific_advice': SpecificAdviceAgent(self.config),
            'comprehensive_decision': ComprehensiveDecisionAgent(self.config)
        }
        
        logger.info("SAGIN多Agent系统初始化完成")
        logger.info(f"运行模式: {'测试模式' if self.config.test_mode else 'LLM模式'}")
    
    def run_collaborative_workflow(self, input_file: str, output_dir: Optional[str] = None) -> Dict[str, Any]:
        """
        运行协作工作流
        
        Args:
            input_file: 输入文件路径
            output_dir: 输出目录路径
            
        Returns:
            工作流执行结果
        """
        logger.info("开始执行协作工作流...")
        
        if output_dir:
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
        else:
            output_path = Path(self.config.data.output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
        
        results = {}
        current_input = input_file
        
        try:
            # 步骤1: Summarization Agent (威胁分类)
            logger.info("步骤1: 运行 SummarizationAgent - 威胁分类")
            summarization_output = output_path / "01_threat_classification.json"
            summarization_result = self.agents['summarization'].run(
                current_input, str(summarization_output)
            )
            results['summarization'] = summarization_result

            # 步骤2: Prompt Agent (描述格式转换)
            logger.info("步骤2: 运行 PromptAgent - 描述格式转换")
            prompt_output = output_path / "02_description_conversion.json"
            # 传递SummarizationAgent的输出数据
            prompt_result = self.agents['prompt'].run(
                current_input, str(prompt_output), input_data=summarization_result
            )
            results['prompt'] = prompt_result

            # 步骤3: Comprehensive Decision Agent (CVSS评分)
            logger.info("步骤3: 运行 ComprehensiveDecisionAgent - CVSS评分")
            decision_output = output_path / "03_cvss_scoring.json"
            # 传递PromptAgent的输出数据
            decision_result = self.agents['comprehensive_decision'].run(
                current_input, str(decision_output), input_data=prompt_result
            )
            results['comprehensive_decision'] = decision_result

            # 步骤4: Specific Advice Agent (策略建议)
            logger.info("步骤4: 运行 SpecificAdviceAgent - 策略建议生成")
            advice_output = output_path / "04_strategy_recommendations.json"
            # 传递ComprehensiveDecisionAgent的输出数据
            advice_result = self.agents['specific_advice'].run(
                current_input, str(advice_output), input_data=decision_result
            )
            results['specific_advice'] = advice_result
            
            # 生成工作流摘要
            workflow_summary = {
                'workflow_type': 'collaborative',
                'input_file': input_file,
                'output_directory': str(output_path),
                'agents_executed': list(results.keys()),
                'final_output': str(advice_output),
                'workflow_steps': [
                    {'step': 1, 'agent': 'SummarizationAgent', 'task': '威胁分类', 'output': str(summarization_output)},
                    {'step': 2, 'agent': 'PromptAgent', 'task': '描述格式转换', 'output': str(prompt_output)},
                    {'step': 3, 'agent': 'ComprehensiveDecisionAgent', 'task': 'CVSS评分', 'output': str(decision_output)},
                    {'step': 4, 'agent': 'SpecificAdviceAgent', 'task': '策略建议生成', 'output': str(advice_output)}
                ],
                'success': True,
                'results': results
            }
            
            # 保存工作流摘要
            summary_file = output_path / "workflow_summary.json"
            import json
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(workflow_summary, f, ensure_ascii=False, indent=2)
            
            logger.info("协作工作流执行完成")
            logger.info(f"最终输出: {advice_output}")
            logger.info("工作流步骤:")
            logger.info("  1. SummarizationAgent: 威胁分类")
            logger.info("  2. PromptAgent: 描述格式转换")
            logger.info("  3. ComprehensiveDecisionAgent: CVSS评分")
            logger.info("  4. SpecificAdviceAgent: 策略建议生成")
            
            return workflow_summary
            
        except Exception as e:
            logger.error(f"协作工作流执行失败: {e}")
            error_summary = {
                'workflow_type': 'collaborative',
                'input_file': input_file,
                'success': False,
                'error': str(e),
                'completed_agents': list(results.keys())
            }
            return error_summary
    
    def run_independent_agent(self, agent_name: str, input_file: str, 
                            output_file: Optional[str] = None) -> Dict[str, Any]:
        """
        运行独立Agent
        
        Args:
            agent_name: Agent名称
            input_file: 输入文件路径
            output_file: 输出文件路径
            
        Returns:
            Agent执行结果
        """
        if agent_name not in self.agents:
            raise ValueError(f"未知的Agent: {agent_name}. 可用的Agent: {list(self.agents.keys())}")
        
        logger.info(f"运行独立Agent: {agent_name}")
        
        try:
            agent = self.agents[agent_name]
            result = agent.run(input_file, output_file)
            
            logger.info(f"Agent {agent_name} 执行完成")
            return {
                'agent_name': agent_name,
                'input_file': input_file,
                'success': True,
                'result': result
            }
            
        except Exception as e:
            logger.error(f"Agent {agent_name} 执行失败: {e}")
            return {
                'agent_name': agent_name,
                'input_file': input_file,
                'success': False,
                'error': str(e)
            }
    
    def list_agents(self) -> Dict[str, str]:
        """
        列出所有可用的Agent
        
        Returns:
            Agent名称和描述的字典
        """
        agent_descriptions = {
            'summarization': 'Summarization Agent - 威胁数据摘要生成',
            'prompt': 'Prompt Agent - 提示词生成和数据处理',
            'specific_advice': 'Specific Advice Agent - 特定建议生成',
            'comprehensive_decision': 'Comprehensive Decision Agent - 综合决策生成'
        }
        return agent_descriptions
    
    def get_system_info(self) -> Dict[str, Any]:
        """
        获取系统信息
        
        Returns:
            系统信息字典
        """
        return {
            'system_name': 'SAGIN Multi-Agent Threat Analysis System',
            'version': '2.0.0',
            'mode': '测试模式' if self.config.test_mode else 'LLM模式',
            'available_agents': list(self.agents.keys()),
            'config_summary': {
                'test_mode': self.config.test_mode,
                'llm_type': self.config.model.llm_type,
                'model_name': self.config.model.model_name,
                'data_dir': self.config.data.data_dir,
                'output_dir': self.config.data.output_dir
            }
        }


def create_parser() -> argparse.ArgumentParser:
    """创建命令行参数解析器"""
    
    parser = argparse.ArgumentParser(
        description='SAGIN Multi-Agent Threat Analysis System',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:

1. 运行协作工作流:
   python main.py workflow --input data/input/threat_data.json --output data/output

2. 运行独立Agent:
   python main.py agent summarization --input data/input/threat_data.json --output result.json

3. 列出所有Agent:
   python main.py list-agents

4. 显示系统信息:
   python main.py info
        """
    )
    
    parser.add_argument('--config', '-c', type=str, help='配置文件路径')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 工作流命令
    workflow_parser = subparsers.add_parser('workflow', help='运行协作工作流')
    workflow_parser.add_argument('--input', '-i', required=True, help='输入文件路径')
    workflow_parser.add_argument('--output', '-o', help='输出目录路径')
    
    # Agent命令
    agent_parser = subparsers.add_parser('agent', help='运行独立Agent')
    agent_parser.add_argument('agent_name', choices=['summarization', 'prompt', 'specific_advice', 'comprehensive_decision'], help='Agent名称')
    agent_parser.add_argument('--input', '-i', required=True, help='输入文件路径')
    agent_parser.add_argument('--output', '-o', help='输出文件路径')
    
    # 列表命令
    subparsers.add_parser('list-agents', help='列出所有可用的Agent')
    
    # 信息命令
    subparsers.add_parser('info', help='显示系统信息')
    
    return parser


def main():
    """主函数"""
    
    parser = create_parser()
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        import logging
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        # 初始化系统
        system = SAGINMultiAgentSystem(args.config)
        
        if args.command == 'workflow':
            # 运行协作工作流
            result = system.run_collaborative_workflow(args.input, args.output)
            
            if result['success']:
                print(f"\n✅ 协作工作流执行成功!")
                print(f"📁 输出目录: {result['output_directory']}")
                print(f"📄 最终输出: {result['final_output']}")
                print(f"🤖 执行的Agent: {', '.join(result['agents_executed'])}")
            else:
                print(f"\n❌ 协作工作流执行失败: {result['error']}")
                if 'completed_agents' in result:
                    print(f"✅ 已完成的Agent: {', '.join(result['completed_agents'])}")
                sys.exit(1)
        
        elif args.command == 'agent':
            # 运行独立Agent
            result = system.run_independent_agent(args.agent_name, args.input, args.output)
            
            if result['success']:
                print(f"\n✅ Agent {args.agent_name} 执行成功!")
                if 'output_file' in result['result']:
                    print(f"📄 输出文件: {result['result']['output_file']}")
            else:
                print(f"\n❌ Agent {args.agent_name} 执行失败: {result['error']}")
                sys.exit(1)
        
        elif args.command == 'list-agents':
            # 列出所有Agent
            agents = system.list_agents()
            print("\n🤖 可用的Agent:")
            print("=" * 50)
            for name, description in agents.items():
                print(f"  {name:<20} - {description}")
        
        elif args.command == 'info':
            # 显示系统信息
            info = system.get_system_info()
            print(f"\n📊 {info['system_name']}")
            print("=" * 50)
            print(f"版本: {info['version']}")
            print(f"运行模式: {info['mode']}")
            print(f"LLM类型: {info['config_summary']['llm_type']}")
            print(f"模型名称: {info['config_summary']['model_name']}")
            print(f"数据目录: {info['config_summary']['data_dir']}")
            print(f"输出目录: {info['config_summary']['output_dir']}")
            print(f"可用Agent: {', '.join(info['available_agents'])}")
        
        else:
            parser.print_help()
    
    except Exception as e:
        logger.error(f"系统执行失败: {e}")
        print(f"\n❌ 系统执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
