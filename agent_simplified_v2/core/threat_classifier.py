"""
统一威胁分类模块
"""

import re
from typing import Dict, List, Any, Tuple
from .logger_config import get_logger

logger = get_logger(__name__)


class ThreatClassifier:
    """统一威胁分类器"""
    
    def __init__(self):
        # 威胁类型关键词映射（统一定义，消除重复）
        self.threat_keywords = {
            "DDoS": [
                "ddos", "denial of service", "flood", "loit", "dos attack",
                "traffic flood", "bandwidth exhaustion", "service unavailable"
            ],
            "Infiltration": [
                "brute force", "login", "ssh", "unauthorized", "infiltration",
                "credential stuffing", "password attack", "unauthorized access"
            ],
            "Port Scan": [
                "port scan", "nmap", "scanning", "port probe", "network scan",
                "service discovery", "reconnaissance", "port enumeration"
            ],
            "Eavesdropping": [
                "eavesdrop", "intercept", "unencrypted", "sniffing", "wiretap",
                "packet capture", "traffic analysis", "man in the middle"
            ],
            "ARP Spoofing": [
                "arp", "spoofing", "poisoning", "arp cache", "network spoofing",
                "mac address", "layer 2 attack"
            ],
            "SQL Injection": [
                "sql", "injection", "sqli", "database attack", "sql query",
                "union select", "drop table", "malicious query"
            ],
            "Malware": [
                "malware", "virus", "trojan", "worm", "ransomware",
                "backdoor", "rootkit", "spyware", "adware"
            ],
            "Web Attack": [
                "xss", "csrf", "web attack", "http attack", "web vulnerability",
                "cross site", "code injection", "path traversal"
            ],
            "Network Attack": [
                "network attack", "protocol attack", "tcp attack", "udp flood",
                "icmp flood", "syn flood", "network intrusion"
            ]
        }
        
        # 威胁严重性映射
        self.threat_severity = {
            "DDoS": 8.5,
            "Infiltration": 9.0,
            "Port Scan": 4.0,
            "Eavesdropping": 7.5,
            "ARP Spoofing": 6.5,
            "SQL Injection": 8.0,
            "Malware": 9.5,
            "Web Attack": 7.0,
            "Network Attack": 6.0,
            "Unknown": 5.0
        }
    
    def classify_threat_from_description(self, description: str) -> Dict[str, Any]:
        """
        从描述中分类威胁
        
        Args:
            description: 威胁描述文本
            
        Returns:
            分类结果字典，包含威胁类型、置信度、匹配关键词等
        """
        if not description:
            return self._get_unknown_result()
        
        desc_lower = description.lower()
        threat_scores = {}
        
        # 计算每种威胁类型的匹配分数
        for threat_type, keywords in self.threat_keywords.items():
            score = 0
            matched_keywords = []
            
            for keyword in keywords:
                if keyword in desc_lower:
                    score += 1
                    matched_keywords.append(keyword)
            
            if score > 0:
                threat_scores[threat_type] = {
                    'score': score,
                    'matched_keywords': matched_keywords
                }
        
        # 选择得分最高的威胁类型
        if threat_scores:
            best_match = max(threat_scores.items(), key=lambda x: x[1]['score'])
            threat_type = best_match[0]
            score_info = best_match[1]
            
            # 计算置信度（归一化到0-1）
            confidence = min(score_info['score'] / 3.0, 1.0)
            
            return {
                'threat_type': threat_type,
                'confidence': confidence,
                'matched_keywords': score_info['matched_keywords'],
                'severity_score': self.threat_severity.get(threat_type, 5.0),
                'all_scores': threat_scores
            }
        else:
            # 尝试基于协议和上下文推断
            inferred_type = self._classify_by_protocol_and_context(description)
            return {
                'threat_type': inferred_type,
                'confidence': 0.3,
                'matched_keywords': ["基于协议和上下文推断"],
                'severity_score': self.threat_severity.get(inferred_type, 5.0),
                'all_scores': {}
            }
    
    def _classify_by_protocol_and_context(self, description: str) -> str:
        """基于协议和上下文进行威胁分类的后备方法"""
        desc_lower = description.lower()
        
        # 基于协议和端口的简单推断
        if 'tcp' in desc_lower and ('port' in desc_lower or 'flood' in desc_lower):
            return 'DDoS'
        elif 'http' in desc_lower or 'web' in desc_lower:
            return 'Web Attack'
        elif 'network' in desc_lower and 'traffic' in desc_lower:
            return 'Network Attack'
        elif 'ssh' in desc_lower or 'login' in desc_lower:
            return 'Infiltration'
        else:
            return 'Unknown'
    
    def _get_unknown_result(self) -> Dict[str, Any]:
        """返回未知威胁的默认结果"""
        return {
            'threat_type': 'Unknown',
            'confidence': 0.1,
            'matched_keywords': [],
            'severity_score': 5.0,
            'all_scores': {}
        }
    
    def batch_classify(self, descriptions: List[str]) -> List[Dict[str, Any]]:
        """
        批量分类威胁
        
        Args:
            descriptions: 威胁描述列表
            
        Returns:
            分类结果列表
        """
        results = []
        for i, desc in enumerate(descriptions):
            try:
                result = self.classify_threat_from_description(desc)
                result['index'] = i
                results.append(result)
            except Exception as e:
                logger.error(f"威胁分类失败 (索引 {i}): {e}")
                result = self._get_unknown_result()
                result['index'] = i
                result['error'] = str(e)
                results.append(result)
        
        return results
    
    def get_threat_statistics(self, classification_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        获取威胁分类统计信息
        
        Args:
            classification_results: 分类结果列表
            
        Returns:
            统计信息字典
        """
        stats = {
            'total_threats': len(classification_results),
            'threat_types': {},
            'confidence_distribution': {'high': 0, 'medium': 0, 'low': 0},
            'average_confidence': 0.0,
            'severity_distribution': {'critical': 0, 'high': 0, 'medium': 0, 'low': 0}
        }
        
        total_confidence = 0
        
        for result in classification_results:
            threat_type = result.get('threat_type', 'Unknown')
            confidence = result.get('confidence', 0.0)
            severity = result.get('severity_score', 5.0)
            
            # 威胁类型统计
            stats['threat_types'][threat_type] = stats['threat_types'].get(threat_type, 0) + 1
            
            # 置信度分布
            if confidence >= 0.8:
                stats['confidence_distribution']['high'] += 1
            elif confidence >= 0.5:
                stats['confidence_distribution']['medium'] += 1
            else:
                stats['confidence_distribution']['low'] += 1
            
            total_confidence += confidence
            
            # 严重性分布
            if severity >= 9.0:
                stats['severity_distribution']['critical'] += 1
            elif severity >= 7.0:
                stats['severity_distribution']['high'] += 1
            elif severity >= 5.0:
                stats['severity_distribution']['medium'] += 1
            else:
                stats['severity_distribution']['low'] += 1
        
        # 计算平均置信度
        if classification_results:
            stats['average_confidence'] = total_confidence / len(classification_results)
        
        return stats
    
    def get_supported_threat_types(self) -> List[str]:
        """获取支持的威胁类型列表"""
        return list(self.threat_keywords.keys())
    
    def add_custom_threat_type(self, threat_type: str, keywords: List[str], severity: float = 5.0):
        """
        添加自定义威胁类型
        
        Args:
            threat_type: 威胁类型名称
            keywords: 关键词列表
            severity: 严重性评分 (0-10)
        """
        self.threat_keywords[threat_type] = keywords
        self.threat_severity[threat_type] = severity
        logger.info(f"已添加自定义威胁类型: {threat_type}")


# 全局威胁分类器实例
threat_classifier = ThreatClassifier()
