"""
评估指标模块
提供各种准确率和误差计算方法
"""

import numpy as np
from typing import List, Dict, Any, Union, Tuple
from .logger_config import get_logger

logger = get_logger(__name__)


class Evaluator:
    """评估指标计算器"""
    
    def __init__(self):
        pass
    
    def calculate_classification_accuracy(self, predictions: List[str], ground_truth: List[str]) -> Dict[str, float]:
        """
        计算分类准确率
        
        Args:
            predictions: 预测结果列表
            ground_truth: 真实标签列表
            
        Returns:
            包含准确率指标的字典
        """
        if len(predictions) != len(ground_truth):
            logger.error(f"预测结果和真实标签长度不匹配: {len(predictions)} vs {len(ground_truth)}")
            return {"accuracy": 0.0, "total": 0, "correct": 0}
        
        if not predictions:
            logger.warning("预测结果为空")
            return {"accuracy": 0.0, "total": 0, "correct": 0}
        
        correct = sum(1 for pred, true in zip(predictions, ground_truth) if pred == true)
        total = len(predictions)
        accuracy = correct / total if total > 0 else 0.0
        
        logger.info(f"分类准确率: {accuracy:.4f} ({correct}/{total})")
        
        return {
            "accuracy": accuracy,
            "total": total,
            "correct": correct,
            "error_rate": 1 - accuracy
        }
    
    def calculate_cvss_score_error(self, predicted_scores: List[float], true_scores: List[float]) -> Dict[str, float]:
        """
        计算CVSS评分误差
        
        Args:
            predicted_scores: 预测的CVSS分数列表
            true_scores: 真实的CVSS分数列表
            
        Returns:
            包含误差指标的字典
        """
        if len(predicted_scores) != len(true_scores):
            logger.error(f"预测分数和真实分数长度不匹配: {len(predicted_scores)} vs {len(true_scores)}")
            return {"mae": float('inf'), "mse": float('inf'), "rmse": float('inf')}
        
        if not predicted_scores:
            logger.warning("预测分数为空")
            return {"mae": 0.0, "mse": 0.0, "rmse": 0.0}
        
        pred_array = np.array(predicted_scores)
        true_array = np.array(true_scores)
        
        # 计算各种误差指标
        mae = np.mean(np.abs(pred_array - true_array))
        mse = np.mean((pred_array - true_array) ** 2)
        rmse = np.sqrt(mse)
        
        logger.info(f"CVSS评分误差 - MAE: {mae:.4f}, MSE: {mse:.4f}, RMSE: {rmse:.4f}")
        
        return {
            "mae": float(mae),
            "mse": float(mse),
            "rmse": float(rmse),
            "total": len(predicted_scores)
        }
    
    def calculate_cvss_metrics_accuracy(self, predicted_metrics: List[List], true_metrics: List[List]) -> Dict[str, float]:
        """
        计算CVSS指标数组的准确率
        
        Args:
            predicted_metrics: 预测的CVSS指标数组列表
            true_metrics: 真实的CVSS指标数组列表
            
        Returns:
            包含准确率指标的字典
        """
        if len(predicted_metrics) != len(true_metrics):
            logger.error(f"预测指标和真实指标长度不匹配: {len(predicted_metrics)} vs {len(true_metrics)}")
            return {"exact_match": 0.0, "element_accuracy": 0.0}
        
        if not predicted_metrics:
            logger.warning("预测指标为空")
            return {"exact_match": 0.0, "element_accuracy": 0.0}
        
        # 完全匹配准确率
        exact_matches = sum(1 for pred, true in zip(predicted_metrics, true_metrics) if pred == true)
        exact_match_rate = exact_matches / len(predicted_metrics)
        
        # 元素级准确率
        total_elements = 0
        correct_elements = 0
        
        for pred, true in zip(predicted_metrics, true_metrics):
            if len(pred) == len(true):
                total_elements += len(pred)
                correct_elements += sum(1 for p, t in zip(pred, true) if p == t)
            else:
                logger.warning(f"指标数组长度不匹配: {len(pred)} vs {len(true)}")
        
        element_accuracy = correct_elements / total_elements if total_elements > 0 else 0.0
        
        logger.info(f"CVSS指标准确率 - 完全匹配: {exact_match_rate:.4f}, 元素准确率: {element_accuracy:.4f}")
        
        return {
            "exact_match": exact_match_rate,
            "element_accuracy": element_accuracy,
            "total_samples": len(predicted_metrics),
            "exact_matches": exact_matches,
            "total_elements": total_elements,
            "correct_elements": correct_elements
        }
    
    def calculate_strategy_matching_rate(self, predicted_strategies: List[List[str]], true_strategies: List[List[str]]) -> Dict[str, float]:
        """
        计算策略匹配率
        
        Args:
            predicted_strategies: 预测的策略建议列表
            true_strategies: 真实的策略建议列表
            
        Returns:
            包含匹配率指标的字典
        """
        if len(predicted_strategies) != len(true_strategies):
            logger.error(f"预测策略和真实策略长度不匹配: {len(predicted_strategies)} vs {len(true_strategies)}")
            return {"exact_match": 0.0, "partial_match": 0.0, "jaccard_similarity": 0.0}
        
        if not predicted_strategies:
            logger.warning("预测策略为空")
            return {"exact_match": 0.0, "partial_match": 0.0, "jaccard_similarity": 0.0}
        
        exact_matches = 0
        partial_matches = 0
        jaccard_scores = []
        
        for pred, true in zip(predicted_strategies, true_strategies):
            pred_set = set(pred) if isinstance(pred, list) else set([pred])
            true_set = set(true) if isinstance(true, list) else set([true])
            
            # 完全匹配
            if pred_set == true_set:
                exact_matches += 1
                partial_matches += 1
            # 部分匹配（有交集）
            elif pred_set & true_set:
                partial_matches += 1
            
            # Jaccard相似度
            intersection = len(pred_set & true_set)
            union = len(pred_set | true_set)
            jaccard = intersection / union if union > 0 else 0.0
            jaccard_scores.append(jaccard)
        
        total = len(predicted_strategies)
        exact_match_rate = exact_matches / total
        partial_match_rate = partial_matches / total
        avg_jaccard = np.mean(jaccard_scores) if jaccard_scores else 0.0
        
        logger.info(f"策略匹配率 - 完全匹配: {exact_match_rate:.4f}, 部分匹配: {partial_match_rate:.4f}, Jaccard: {avg_jaccard:.4f}")
        
        return {
            "exact_match": exact_match_rate,
            "partial_match": partial_match_rate,
            "jaccard_similarity": float(avg_jaccard),
            "total_samples": total,
            "exact_matches": exact_matches,
            "partial_matches": partial_matches
        }
    
    def calculate_comprehensive_metrics(self, agent_type: str, predictions: Any, ground_truth: Any) -> Dict[str, Any]:
        """
        根据Agent类型计算综合评估指标
        
        Args:
            agent_type: Agent类型 ('summarization', 'comprehensive', 'specific')
            predictions: 预测结果
            ground_truth: 真实标签
            
        Returns:
            综合评估指标字典
        """
        if agent_type == 'summarization':
            return self.calculate_classification_accuracy(predictions, ground_truth)
        
        elif agent_type == 'comprehensive':
            if isinstance(predictions, dict) and isinstance(ground_truth, dict):
                # 分别计算分数和指标的准确率
                score_metrics = self.calculate_cvss_score_error(
                    predictions.get('scores', []), 
                    ground_truth.get('scores', [])
                )
                metrics_accuracy = self.calculate_cvss_metrics_accuracy(
                    predictions.get('metrics', []), 
                    ground_truth.get('metrics', [])
                )
                return {**score_metrics, **metrics_accuracy}
            else:
                logger.error("CVSS数据格式错误")
                return {}
        
        elif agent_type == 'specific':
            return self.calculate_strategy_matching_rate(predictions, ground_truth)
        
        else:
            logger.error(f"未知的Agent类型: {agent_type}")
            return {}


# 全局评估器实例
evaluator = Evaluator()


# 便捷函数
def evaluate_classification(predictions: List[str], ground_truth: List[str]) -> Dict[str, float]:
    """分类评估便捷函数"""
    return evaluator.calculate_classification_accuracy(predictions, ground_truth)


def evaluate_cvss_scores(predicted_scores: List[float], true_scores: List[float]) -> Dict[str, float]:
    """CVSS评分评估便捷函数"""
    return evaluator.calculate_cvss_score_error(predicted_scores, true_scores)


def evaluate_strategies(predicted_strategies: List[List[str]], true_strategies: List[List[str]]) -> Dict[str, float]:
    """策略匹配评估便捷函数"""
    return evaluator.calculate_strategy_matching_rate(predicted_strategies, true_strategies)
