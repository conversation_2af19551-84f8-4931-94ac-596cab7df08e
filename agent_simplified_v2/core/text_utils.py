"""
文本处理工具模块
"""

import re
import math
from typing import List, Dict, Any, Set, Tuple
from collections import Counter
from .logger_config import get_logger

logger = get_logger(__name__)


class TextUtils:
    """文本处理工具类"""
    
    @staticmethod
    def calculate_text_similarity(text1: str, text2: str, method: str = "jaccard") -> float:
        """
        计算两个文本的相似度
        
        Args:
            text1: 第一个文本
            text2: 第二个文本
            method: 相似度计算方法 ("jaccard", "cosine", "overlap")
            
        Returns:
            相似度分数 (0-1)
        """
        if not text1 or not text2:
            return 0.0
        
        if method == "jaccard":
            return TextUtils._jaccard_similarity(text1, text2)
        elif method == "cosine":
            return TextUtils._cosine_similarity(text1, text2)
        elif method == "overlap":
            return TextUtils._overlap_similarity(text1, text2)
        else:
            raise ValueError(f"不支持的相似度计算方法: {method}")
    
    @staticmethod
    def _jaccard_similarity(text1: str, text2: str) -> float:
        """计算Jaccard相似度"""
        # 提取关键词
        keywords1 = TextUtils.extract_keywords(text1)
        keywords2 = TextUtils.extract_keywords(text2)
        
        # 计算交集和并集
        intersection = len(keywords1.intersection(keywords2))
        union = len(keywords1.union(keywords2))
        
        if union == 0:
            return 0.0
        
        return intersection / union
    
    @staticmethod
    def _cosine_similarity(text1: str, text2: str) -> float:
        """计算余弦相似度"""
        # 获取词频向量
        words1 = TextUtils.tokenize(text1)
        words2 = TextUtils.tokenize(text2)
        
        # 创建词频计数器
        counter1 = Counter(words1)
        counter2 = Counter(words2)
        
        # 获取所有唯一词汇
        all_words = set(counter1.keys()) | set(counter2.keys())
        
        if not all_words:
            return 0.0
        
        # 创建向量
        vector1 = [counter1.get(word, 0) for word in all_words]
        vector2 = [counter2.get(word, 0) for word in all_words]
        
        # 计算点积
        dot_product = sum(a * b for a, b in zip(vector1, vector2))
        
        # 计算向量长度
        magnitude1 = math.sqrt(sum(a * a for a in vector1))
        magnitude2 = math.sqrt(sum(b * b for b in vector2))
        
        if magnitude1 == 0 or magnitude2 == 0:
            return 0.0
        
        return dot_product / (magnitude1 * magnitude2)
    
    @staticmethod
    def _overlap_similarity(text1: str, text2: str) -> float:
        """计算重叠相似度"""
        keywords1 = TextUtils.extract_keywords(text1)
        keywords2 = TextUtils.extract_keywords(text2)
        
        if not keywords1 or not keywords2:
            return 0.0
        
        intersection = len(keywords1.intersection(keywords2))
        min_size = min(len(keywords1), len(keywords2))
        
        return intersection / min_size
    
    @staticmethod
    def extract_keywords(text: str, min_length: int = 2) -> Set[str]:
        """
        提取文本关键词
        
        Args:
            text: 输入文本
            min_length: 最小词长度
            
        Returns:
            关键词集合
        """
        if not text:
            return set()
        
        # 转换为小写并提取单词
        text_lower = text.lower()
        words = re.findall(r'\b\w+\b', text_lower)
        
        # 过滤短词和停用词
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have',
            'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
            'may', 'might', 'can', 'this', 'that', 'these', 'those', 'i', 'you',
            'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them'
        }
        
        keywords = {
            word for word in words 
            if len(word) >= min_length and word not in stop_words
        }
        
        return keywords
    
    @staticmethod
    def tokenize(text: str) -> List[str]:
        """
        文本分词
        
        Args:
            text: 输入文本
            
        Returns:
            词汇列表
        """
        if not text:
            return []
        
        # 转换为小写并提取单词
        text_lower = text.lower()
        words = re.findall(r'\b\w+\b', text_lower)
        
        return words
    
    @staticmethod
    def clean_text(text: str) -> str:
        """
        清理文本
        
        Args:
            text: 输入文本
            
        Returns:
            清理后的文本
        """
        if not text:
            return ""
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text.strip())
        
        # 移除特殊字符（保留基本标点）
        text = re.sub(r'[^\w\s\.\,\!\?\;\:\-\(\)]', '', text)
        
        return text
    
    @staticmethod
    def extract_threat_indicators(text: str) -> List[str]:
        """
        提取威胁指标
        
        Args:
            text: 输入文本
            
        Returns:
            威胁指标列表
        """
        indicators = []
        
        # IP地址模式
        ip_pattern = r'\b(?:\d{1,3}\.){3}\d{1,3}\b'
        ips = re.findall(ip_pattern, text)
        indicators.extend([f"IP:{ip}" for ip in ips])
        
        # 端口号模式
        port_pattern = r'\bport\s+(\d+)\b'
        ports = re.findall(port_pattern, text, re.IGNORECASE)
        indicators.extend([f"Port:{port}" for port in ports])
        
        # 协议模式
        protocol_pattern = r'\b(tcp|udp|http|https|ftp|ssh|dns|icmp)\b'
        protocols = re.findall(protocol_pattern, text, re.IGNORECASE)
        indicators.extend([f"Protocol:{proto.upper()}" for proto in protocols])
        
        # 文件路径模式
        path_pattern = r'[/\\][\w/\\.-]+'
        paths = re.findall(path_pattern, text)
        indicators.extend([f"Path:{path}" for path in paths])
        
        # URL模式
        url_pattern = r'https?://[^\s]+'
        urls = re.findall(url_pattern, text)
        indicators.extend([f"URL:{url}" for url in urls])
        
        return list(set(indicators))  # 去重
    
    @staticmethod
    def calculate_text_complexity(text: str) -> Dict[str, Any]:
        """
        计算文本复杂度指标
        
        Args:
            text: 输入文本
            
        Returns:
            复杂度指标字典
        """
        if not text:
            return {
                'char_count': 0,
                'word_count': 0,
                'sentence_count': 0,
                'avg_word_length': 0.0,
                'avg_sentence_length': 0.0,
                'unique_word_ratio': 0.0
            }
        
        # 基本统计
        char_count = len(text)
        words = TextUtils.tokenize(text)
        word_count = len(words)
        sentences = re.split(r'[.!?]+', text)
        sentence_count = len([s for s in sentences if s.strip()])
        
        # 计算平均值
        avg_word_length = sum(len(word) for word in words) / word_count if word_count > 0 else 0.0
        avg_sentence_length = word_count / sentence_count if sentence_count > 0 else 0.0
        
        # 唯一词汇比例
        unique_words = set(words)
        unique_word_ratio = len(unique_words) / word_count if word_count > 0 else 0.0
        
        return {
            'char_count': char_count,
            'word_count': word_count,
            'sentence_count': sentence_count,
            'avg_word_length': avg_word_length,
            'avg_sentence_length': avg_sentence_length,
            'unique_word_ratio': unique_word_ratio
        }
    
    @staticmethod
    def find_similar_texts(target_text: str, text_list: List[str], 
                          threshold: float = 0.5, method: str = "jaccard") -> List[Tuple[int, str, float]]:
        """
        在文本列表中查找相似文本
        
        Args:
            target_text: 目标文本
            text_list: 文本列表
            threshold: 相似度阈值
            method: 相似度计算方法
            
        Returns:
            相似文本列表 [(索引, 文本, 相似度分数)]
        """
        similar_texts = []
        
        for i, text in enumerate(text_list):
            similarity = TextUtils.calculate_text_similarity(target_text, text, method)
            if similarity >= threshold:
                similar_texts.append((i, text, similarity))
        
        # 按相似度降序排序
        similar_texts.sort(key=lambda x: x[2], reverse=True)
        
        return similar_texts


# 全局文本工具实例
text_utils = TextUtils()
