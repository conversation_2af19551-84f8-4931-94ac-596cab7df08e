"""
统一配置管理模块
"""

import os
import json
from dataclasses import dataclass, asdict
from pathlib import Path
from typing import Optional, Dict, Any
from .logger_config import get_logger

logger = get_logger(__name__)


@dataclass
class ModelConfig:
    """模型配置"""
    llm_type: str = "local"  # local, openai
    model_name: str = "llama3"
    model_path: str = "./models/llama3-7b"
    api_key: str = ""
    api_base: str = ""
    base_url: str = "http://localhost:11434"
    quantization: bool = True
    max_tokens: int = 4096
    temperature: float = 0.7
    top_p: float = 0.9
    device: str = "auto"
    embedding_model: str = "all-MiniLM-L6-v2"
    embedding_device: str = "cpu"


@dataclass
class DataConfig:
    """数据配置"""
    data_dir: str = "./data"
    input_dir: str = "./data/input"
    output_dir: str = "./data/output"
    threat_data_file: str = "threat_data.json"
    cvss_data_file: str = "cvss_data.json"
    strategy_data_file: str = "strategy_data.json"
    supported_formats: list = None

    def __post_init__(self):
        if self.supported_formats is None:
            self.supported_formats = ['json', 'csv', 'txt']
    vector_db_path: str = "./vector_db"


@dataclass
class Config:
    """主配置类"""
    # 运行模式
    test_mode: bool = True
    verbose: bool = False
    
    # 子配置
    model: ModelConfig = None
    data: DataConfig = None
    
    # Agent配置
    agent_configs: Dict[str, Any] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.model is None:
            self.model = ModelConfig()
        if self.data is None:
            self.data = DataConfig()
        if self.agent_configs is None:
            self.agent_configs = {
                "summarization": {
                    "embedding_model": "all-MiniLM-L6-v2",
                    "max_threats": 100
                },
                "prompt": {
                    "max_prompts": 50,
                    "reasoning_steps": 5
                },
                "advice": {
                    "similarity_threshold": 0.7,
                    "max_strategies": 10
                },
                "decision": {
                    "decision_threshold": 0.5,
                    "conflict_resolution": "weighted_average"
                }
            }
    
    @classmethod
    def from_file(cls, config_file: str) -> 'Config':
        """从配置文件加载"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 创建子配置对象
            model_config = ModelConfig(**data.get('model', {}))
            data_config = DataConfig(**data.get('data', {}))
            
            # 创建主配置
            config = cls(
                test_mode=data.get('test_mode', True),
                verbose=data.get('verbose', False),
                model=model_config,
                data=data_config,
                agent_configs=data.get('agent_configs', {})
            )
            
            logger.info(f"配置已从文件加载: {config_file}")
            return config
            
        except Exception as e:
            logger.warning(f"配置文件加载失败: {e}，使用默认配置")
            return cls()

    @classmethod
    def from_dict(cls, config_data: dict) -> 'Config':
        """从字典数据创建配置"""
        try:
            # 提取model配置，过滤掉不属于ModelConfig的字段
            model_data = config_data.get('model', {})
            model_config_data = {
                k: v for k, v in model_data.items()
                if k in ['llm_type', 'model_name', 'model_path', 'api_key', 'api_base', 'base_url',
                        'quantization', 'max_tokens', 'temperature', 'top_p', 'device',
                        'embedding_model', 'embedding_device']
            }
            model_config = ModelConfig(**model_config_data)

            # 提取data配置
            data_data = config_data.get('data', {})
            data_config_data = {
                k: v for k, v in data_data.items()
                if k in ['data_dir', 'input_dir', 'output_dir', 'threat_data_file',
                        'cvss_data_file', 'strategy_data_file', 'supported_formats']
            }
            data_config = DataConfig(**data_config_data)

            # 创建主配置
            config = cls(
                test_mode=config_data.get('model', {}).get('test_mode', True),
                verbose=config_data.get('verbose', False),
                model=model_config,
                data=data_config,
                agent_configs=config_data.get('agents', {})
            )

            logger.info("配置已从字典数据创建")
            return config

        except Exception as e:
            logger.warning(f"从字典创建配置失败: {e}，使用默认配置")
            return cls()

    @classmethod
    def from_env(cls) -> 'Config':
        """从环境变量加载"""
        return cls(
            test_mode=os.getenv('SAGIN_TEST_MODE', 'true').lower() == 'true',
            verbose=os.getenv('SAGIN_VERBOSE', 'false').lower() == 'true',
            model=ModelConfig(
                llm_type=os.getenv('SAGIN_LLM_TYPE', 'local'),
                model_name=os.getenv('SAGIN_MODEL_NAME', 'llama3'),
                model_path=os.getenv('SAGIN_MODEL_PATH', './models/llama3-7b'),
                api_key=os.getenv('SAGIN_API_KEY', ''),
                quantization=os.getenv('SAGIN_QUANTIZATION', 'true').lower() == 'true'
            )
        )
    
    def save_to_file(self, config_file: str):
        """保存配置到文件"""
        config_path = Path(config_file)
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(asdict(self), f, indent=2, ensure_ascii=False)
        
        logger.info(f"配置已保存到: {config_file}")


# 全局配置实例
_global_config: Optional[Config] = None


def get_config(mode: str = None, config_file: str = None) -> Config:
    """
    获取全局配置实例
    
    Args:
        mode: 运行模式 ('test' 或 'llm')
        config_file: 配置文件路径
        
    Returns:
        配置实例
    """
    global _global_config
    
    if _global_config is None:
        # 首次创建配置
        if config_file and os.path.exists(config_file):
            _global_config = Config.from_file(config_file)
        else:
            _global_config = Config.from_env()
    
    # 根据模式参数调整配置
    if mode is not None:
        _global_config.test_mode = (mode == 'test')
    
    return _global_config


def set_global_config(config: Config):
    """设置全局配置"""
    global _global_config
    _global_config = config
