"""
统一数据加载模块
"""

import json
import csv
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any, Optional, Union, Tuple
from .logger_config import get_logger

logger = get_logger(__name__)


class DataLoader:
    """统一数据加载器"""
    
    def __init__(self):
        self.supported_formats = ['.json', '.csv', '.txt']
    
    def load_json_data(self, file_path: str, validate_format: bool = True) -> Any:
        """
        加载JSON数据
        
        Args:
            file_path: 文件路径
            validate_format: 是否验证数据格式
            
        Returns:
            加载的数据
            
        Raises:
            FileNotFoundError: 文件不存在
            json.JSONDecodeError: JSON格式错误
        """
        try:
            file_path = Path(file_path)
            
            # 检查文件是否存在
            if not file_path.exists():
                raise FileNotFoundError(f"数据文件不存在: {file_path}")
            
            # 读取JSON文件
            logger.info(f"正在加载数据: {file_path}")
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 基本数据验证
            if validate_format:
                self._validate_data_format(data, str(file_path))
            
            logger.info(f"数据加载成功: {file_path} ({self._get_data_info(data)})")
            return data
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON格式错误 {file_path}: {e}")
            raise
        except Exception as e:
            logger.error(f"数据加载失败 {file_path}: {e}")
            raise
    
    def load_csv_data(self, file_path: str) -> List[Dict[str, Any]]:
        """加载CSV数据"""
        try:
            df = pd.read_csv(file_path)
            data = df.to_dict('records')
            logger.info(f"CSV数据加载成功: {file_path} ({len(data)} 条记录)")
            return data
        except Exception as e:
            logger.error(f"CSV数据加载失败 {file_path}: {e}")
            raise
    
    def load_text_data(self, file_path: str) -> List[str]:
        """加载文本数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = [line.strip() for line in f.readlines() if line.strip()]
            logger.info(f"文本数据加载成功: {file_path} ({len(lines)} 行)")
            return lines
        except Exception as e:
            logger.error(f"文本数据加载失败 {file_path}: {e}")
            raise
    
    def load_data(self, file_path: str, **kwargs) -> Any:
        """
        自动检测格式并加载数据
        
        Args:
            file_path: 文件路径
            **kwargs: 额外参数
            
        Returns:
            加载的数据
        """
        file_path = Path(file_path)
        suffix = file_path.suffix.lower()
        
        if suffix == '.json':
            return self.load_json_data(str(file_path), **kwargs)
        elif suffix == '.csv':
            return self.load_csv_data(str(file_path))
        elif suffix == '.txt':
            return self.load_text_data(str(file_path))
        else:
            raise ValueError(f"不支持的文件格式: {suffix}")
    
    def _validate_data_format(self, data: Any, file_path: str):
        """验证数据格式"""
        if data is None:
            raise ValueError(f"数据为空: {file_path}")
        
        # 基本类型检查
        if isinstance(data, list):
            if len(data) == 0:
                logger.warning(f"数据列表为空: {file_path}")
            else:
                # 检查列表元素类型一致性
                first_type = type(data[0])
                if not all(isinstance(item, first_type) for item in data[:10]):
                    logger.warning(f"数据列表元素类型不一致: {file_path}")
        
        elif isinstance(data, dict):
            if len(data) == 0:
                logger.warning(f"数据字典为空: {file_path}")
    
    def _get_data_info(self, data: Any) -> str:
        """获取数据信息描述"""
        if isinstance(data, list):
            return f"{len(data)} 条记录"
        elif isinstance(data, dict):
            return f"{len(data)} 个字段"
        else:
            return f"类型: {type(data).__name__}"

    def load_threat_classification_data(self, file_path: str, remove_labels: bool = False) -> Tuple[List[List], List[str]]:
        """
        加载威胁分类数据 (output_0515_dataset_fin.json)

        Args:
            file_path: 数据文件路径
            remove_labels: 是否移除标签(威胁类型)用于预测

        Returns:
            Tuple[数据列表, 标签列表]
        """
        try:
            data = self.load_json_data(file_path, validate_format=False)

            if not isinstance(data, list) or not data:
                logger.error("威胁分类数据格式错误：应为非空数组")
                return [], []

            # 数据格式: [false, "ground", "Network Traffic", "DDoS", "时间戳", "描述", "Tag", 0]
            # 索引3是威胁类型标签，索引5是描述文本
            processed_data = []
            labels = []

            for item in data:
                if not isinstance(item, list) or len(item) < 8:
                    logger.warning(f"跳过格式错误的数据项: {item}")
                    continue

                # 提取标签(威胁类型)
                threat_type = item[3]
                labels.append(threat_type)

                if remove_labels:
                    # 移除威胁类型标签，保留其他信息
                    processed_item = item[:3] + item[4:]  # 去除索引3
                else:
                    processed_item = item.copy()

                processed_data.append(processed_item)

            logger.info(f"成功加载威胁分类数据: {len(processed_data)} 条记录")
            return processed_data, labels

        except Exception as e:
            logger.error(f"加载威胁分类数据失败: {e}")
            return [], []

    def load_cvss_data_with_labels(self, file_path: str, remove_labels: bool = False) -> Tuple[List[Dict], List[Dict]]:
        """
        加载CVSS评分数据 (output_0525_finetune_metrics.json)

        Args:
            file_path: 数据文件路径
            remove_labels: 是否移除Metrics和Base Score标签

        Returns:
            Tuple[数据列表, 标签列表]
        """
        try:
            raw_data = self.load_json_data(file_path, validate_format=False)

            # 解析嵌套的数据结构
            processed_data = []
            labels = []

            def extract_cvss_items(data_structure):
                """递归提取CVSS数据项"""
                if isinstance(data_structure, list):
                    for item in data_structure:
                        extract_cvss_items(item)
                elif isinstance(data_structure, dict):
                    if all(key in data_structure for key in ["CVE ID", "Description", "Base Score", "Metrics"]):
                        # 这是一个有效的CVSS数据项
                        label_data = {
                            "Base Score": data_structure["Base Score"],
                            "Metrics": data_structure["Metrics"]
                        }
                        labels.append(label_data)

                        if remove_labels:
                            # 移除Metrics和Base Score
                            processed_item = {
                                "CVE ID": data_structure["CVE ID"],
                                "Time": data_structure.get("Time", ""),
                                "Description": data_structure["Description"]
                            }
                        else:
                            processed_item = data_structure.copy()

                        processed_data.append(processed_item)
                    else:
                        # 递归处理嵌套字典
                        for value in data_structure.values():
                            if isinstance(value, (dict, list)):
                                extract_cvss_items(value)

            extract_cvss_items(raw_data)

            logger.info(f"成功加载CVSS数据: {len(processed_data)} 条记录")
            return processed_data, labels

        except Exception as e:
            logger.error(f"加载CVSS数据失败: {e}")
            return [], []

    def load_strategy_data_with_labels(self, file_path: str, remove_labels: bool = False) -> Tuple[List[Dict], List[List]]:
        """
        加载策略建议数据 (output_1112_strategy_train_data.json)

        Args:
            file_path: 数据文件路径
            remove_labels: 是否移除output标签

        Returns:
            Tuple[数据列表, 标签列表]
        """
        try:
            data = self.load_json_data(file_path, validate_format=False)

            if not isinstance(data, list):
                logger.error("策略数据格式错误：应为数组")
                return [], []

            processed_data = []
            labels = []

            for item in data:
                if not isinstance(item, dict) or "input" not in item or "output" not in item:
                    logger.warning(f"跳过格式错误的策略数据项: {item}")
                    continue

                # 提取标签(策略建议)
                output_strategies = item["output"]
                labels.append(output_strategies)

                if remove_labels:
                    # 移除output字段
                    processed_item = {
                        "instruction": item.get("instruction", ""),
                        "input": item["input"]
                    }
                else:
                    processed_item = item.copy()

                processed_data.append(processed_item)

            logger.info(f"成功加载策略数据: {len(processed_data)} 条记录")
            return processed_data, labels

        except Exception as e:
            logger.error(f"加载策略数据失败: {e}")
            return [], []


# 便捷函数
def load_threat_data(file_path: str = "data/output_0515_dataset_fin.json") -> List:
    """加载威胁数据集"""
    loader = DataLoader()
    return loader.load_json_data(file_path)


def load_cvss_data(file_path: str = "data/output_0525_finetune_metrics.json") -> Any:
    """加载CVSS评分数据"""
    loader = DataLoader()
    return loader.load_json_data(file_path)


def load_strategy_data(file_path: str = "data/output_1112_strategy_train_data.json") -> List:
    """加载策略训练数据"""
    loader = DataLoader()
    return loader.load_json_data(file_path)


def load_all_datasets(data_dir: str = "data") -> Dict[str, Any]:
    """
    批量加载所有数据集
    
    Args:
        data_dir: 数据目录
        
    Returns:
        包含所有数据集的字典
    """
    datasets = {}
    loader = DataLoader()
    
    # 定义数据文件映射
    data_files = {
        'threat_data': 'output_0515_dataset_fin.json',
        'cvss_data': 'output_0525_finetune_metrics.json',
        'strategy_data': 'output_1112_strategy_train_data.json'
    }
    
    for key, filename in data_files.items():
        file_path = Path(data_dir) / filename
        try:
            datasets[key] = loader.load_json_data(str(file_path))
            logger.info(f"{key} 加载成功")
        except Exception as e:
            logger.warning(f"{key} 加载失败: {e}")
            datasets[key] = None
    
    return datasets


# 全局数据加载器实例
data_loader = DataLoader()
