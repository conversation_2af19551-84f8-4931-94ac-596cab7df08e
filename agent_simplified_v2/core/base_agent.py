"""
Agent基类模块
"""

import json
import argparse
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Any, Dict, Optional, Union
from .config_manager import Config, get_config
from .data_loader import DataLoader
from .logger_config import get_logger

logger = get_logger(__name__)


class BaseAgent(ABC):
    """Agent基类，定义统一接口和通用功能"""
    
    def __init__(self, config: Optional[Config] = None):
        """
        初始化Agent
        
        Args:
            config: 配置对象，如果为None则使用全局配置
        """
        self.config = config or get_config()
        self.data_loader = DataLoader()
        self.logger = get_logger(self.__class__.__name__)
        
        # Agent特定配置
        self.agent_name = self.__class__.__name__.lower().replace('agent', '')
        self.agent_config = self.config.agent_configs.get(self.agent_name, {})
        
        # 输出目录
        self.output_dir = Path(self.config.data.output_dir) / self.agent_name
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(f"{self.__class__.__name__} 初始化完成")
        self.logger.info(f"运行模式: {'测试模式' if self.config.test_mode else 'LLM模式'}")
    
    @abstractmethod
    def run(self, input_file: str, output_file: Optional[str] = None) -> Dict[str, Any]:
        """
        运行Agent的主要方法（抽象方法，子类必须实现）
        
        Args:
            input_file: 输入文件路径
            output_file: 输出文件路径（可选）
            
        Returns:
            处理结果字典
        """
        pass
    
    def _test_mode_run(self, input_file: str, output_file: Optional[str] = None) -> Dict[str, Any]:
        """
        测试模式运行（子类可重写）
        
        Args:
            input_file: 输入文件路径
            output_file: 输出文件路径
            
        Returns:
            测试模式处理结果
        """
        self.logger.info("使用测试模式运行")
        return self._mock_process(input_file, output_file)
    
    def _llm_mode_run(self, input_file: str, output_file: Optional[str] = None) -> Dict[str, Any]:
        """
        LLM模式运行（子类可重写）
        
        Args:
            input_file: 输入文件路径
            output_file: 输出文件路径
            
        Returns:
            LLM模式处理结果
        """
        self.logger.info("使用LLM模式运行")
        # 子类应该重写此方法实现真实的LLM处理
        return self._mock_process(input_file, output_file)
    
    def _mock_process(self, input_file: str, output_file: Optional[str] = None) -> Dict[str, Any]:
        """
        模拟处理（用于测试模式）
        
        Args:
            input_file: 输入文件路径
            output_file: 输出文件路径
            
        Returns:
            模拟处理结果
        """
        # 加载输入数据
        input_data = self.load_input_data(input_file)
        
        # 模拟处理
        result = {
            'agent': self.__class__.__name__,
            'mode': 'test',
            'input_file': input_file,
            'input_data_size': len(input_data) if isinstance(input_data, (list, dict)) else 1,
            'status': 'completed',
            'message': f'{self.__class__.__name__} 测试模式处理完成'
        }
        
        # 保存结果
        if output_file:
            self.save_output_data(result, output_file)
        
        return result
    
    def load_input_data(self, input_file: str) -> Any:
        """
        加载输入数据
        
        Args:
            input_file: 输入文件路径
            
        Returns:
            加载的数据
        """
        try:
            data = self.data_loader.load_data(input_file)
            self.logger.info(f"输入数据加载成功: {input_file}")
            return data
        except Exception as e:
            self.logger.error(f"输入数据加载失败: {e}")
            raise
    
    def save_output_data(self, data: Any, output_file: str):
        """
        保存输出数据
        
        Args:
            data: 要保存的数据
            output_file: 输出文件路径
        """
        try:
            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"输出数据保存成功: {output_file}")
        except Exception as e:
            self.logger.error(f"输出数据保存失败: {e}")
            raise
    
    def get_default_output_file(self, input_file: str, suffix: str = "_output") -> str:
        """
        生成默认输出文件路径
        
        Args:
            input_file: 输入文件路径
            suffix: 文件名后缀
            
        Returns:
            默认输出文件路径
        """
        input_path = Path(input_file)
        output_filename = f"{input_path.stem}{suffix}.json"
        return str(self.output_dir / output_filename)
    
    def validate_input_data(self, data: Any) -> bool:
        """
        验证输入数据格式（子类可重写）
        
        Args:
            data: 输入数据
            
        Returns:
            验证是否通过
        """
        if data is None:
            self.logger.error("输入数据为空")
            return False
        
        return True
    
    def get_processing_stats(self, input_data: Any, output_data: Any) -> Dict[str, Any]:
        """
        获取处理统计信息
        
        Args:
            input_data: 输入数据
            output_data: 输出数据
            
        Returns:
            统计信息字典
        """
        stats = {
            'agent': self.__class__.__name__,
            'mode': 'test' if self.config.test_mode else 'llm',
            'input_size': len(input_data) if isinstance(input_data, (list, dict)) else 1,
            'output_size': len(output_data) if isinstance(output_data, (list, dict)) else 1,
        }
        
        return stats
    
    def run_from_command_line(self):
        """从命令行运行Agent"""
        parser = self.create_argument_parser()
        args = parser.parse_args()
        
        # 更新配置
        if hasattr(args, 'mode') and args.mode:
            self.config.test_mode = (args.mode == 'test')
        
        if hasattr(args, 'verbose') and args.verbose:
            self.config.verbose = True
        
        # 运行Agent
        try:
            result = self.run(args.input, args.output)
            
            if args.output:
                print(f"✅ {self.__class__.__name__} 处理完成，结果已保存到: {args.output}")
            else:
                print(f"✅ {self.__class__.__name__} 处理完成")
                print(json.dumps(result, indent=2, ensure_ascii=False))
                
        except Exception as e:
            print(f"❌ {self.__class__.__name__} 处理失败: {e}")
            if self.config.verbose:
                import traceback
                traceback.print_exc()
    
    def create_argument_parser(self) -> argparse.ArgumentParser:
        """
        创建命令行参数解析器（子类可重写以添加特定参数）
        
        Returns:
            参数解析器
        """
        parser = argparse.ArgumentParser(
            description=f'{self.__class__.__name__} - SAGIN多Agent威胁分析系统'
        )
        
        parser.add_argument(
            '--input', '-i', 
            required=True,
            help='输入文件路径'
        )
        
        parser.add_argument(
            '--output', '-o',
            help='输出文件路径（可选）'
        )
        
        parser.add_argument(
            '--mode', '-m',
            choices=['test', 'llm'],
            default='test',
            help='运行模式：test(测试) 或 llm(大模型)'
        )
        
        parser.add_argument(
            '--verbose', '-v',
            action='store_true',
            help='显示详细日志'
        )
        
        parser.add_argument(
            '--config', '-c',
            help='配置文件路径'
        )
        
        return parser
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.__class__.__name__}(mode={'test' if self.config.test_mode else 'llm'})"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return f"{self.__class__.__name__}(config={self.config}, agent_config={self.agent_config})"
