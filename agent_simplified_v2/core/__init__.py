"""
SAGIN多Agent威胁分析系统 - 核心共享模块
"""

__version__ = "2.0.0"
__author__ = "SAGIN Team"

# 导出核心类和函数
from .config_manager import Config, get_config
from .base_agent import BaseAgent
from .data_loader import DataLoader
from .threat_classifier import ThreatClassifier
from .text_utils import TextUtils
from .logger_config import setup_logger

__all__ = [
    'Config',
    'get_config', 
    'BaseAgent',
    'DataLoader',
    'ThreatClassifier',
    'TextUtils',
    'setup_logger'
]
