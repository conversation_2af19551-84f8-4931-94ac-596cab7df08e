# SAGIN Multi-Agent System Configuration
# 默认配置文件

# 模型配置
model:
  # 模式选择: test 或 llm
  test_mode: true
  
  # LLM配置 (当test_mode为false时使用)
  llm_type: "local"  # local 或 openai
  model_name: "llama3"
  model_path: "meta-llama/Llama-3.1-8B-Instruct"
  api_key: ""  # OpenAI API密钥
  base_url: "http://localhost:11434"  # Ollama本地服务地址
  temperature: 0.7
  max_tokens: 2048
  
  # 嵌入模型配置
  embedding_model: "all-MiniLM-L6-v2"
  embedding_device: "cpu"

# 数据配置
data:
  # 数据目录
  data_dir: "./data"
  
  # 输入输出目录
  input_dir: "./data/input"
  output_dir: "./data/output"
  
  # 数据文件
  threat_data_file: "threat_data.json"
  cvss_data_file: "cvss_data.json"
  strategy_data_file: "strategy_data.json"
  
  # 支持的文件格式
  supported_formats: ["json", "csv", "txt"]

# Agent配置
agents:
  # Summarization Agent配置
  summarization:
    max_summary_length: 500
    summary_style: "technical"
    include_metrics: true
    
  # Prompt Agent配置  
  prompt:
    max_prompts: 10
    prompt_complexity: "medium"
    include_examples: true
    
  # Specific Advice Agent配置
  specific_advice:
    chain_of_thought_steps: 5
    cvss_version: "3.1"
    max_recommendations: 10
    
  # Comprehensive Decision Agent配置
  comprehensive_decision:
    decision_threshold: 0.5
    max_strategies: 5
    conflict_resolution_method: "weighted_average"

# 威胁分类配置
threat_classification:
  # 威胁类型
  threat_types:
    - "GPS_SPOOFING"
    - "JAMMING" 
    - "EAVESDROPPING"
    - "DENIAL_OF_SERVICE"
    - "MALWARE"
    - "INSIDER_THREAT"
    - "PHYSICAL_ATTACK"
    - "DDoS"
    - "DoS"
    - "Infiltration"
    - "Port Scan"
    - "Web Attack"
  
  # 威胁权重映射
  threat_weights:
    GPS_SPOOFING: 0.9
    JAMMING: 0.85
    EAVESDROPPING: 0.8
    DENIAL_OF_SERVICE: 0.88
    MALWARE: 0.92
    INSIDER_THREAT: 0.87
    PHYSICAL_ATTACK: 0.75
    DDoS: 0.88
    DoS: 0.85
    Infiltration: 0.87
    "Port Scan": 0.6
    "Web Attack": 0.7
    GENERAL: 0.6

# 文本处理配置
text_processing:
  # 相似度计算方法
  similarity_methods:
    - "jaccard"
    - "cosine" 
    - "overlap"
  
  # 相似度阈值
  similarity_threshold: 0.6
  
  # 文本预处理
  preprocessing:
    lowercase: true
    remove_punctuation: true
    remove_stopwords: false

# CVSS配置
cvss:
  version: "3.1"
  
  # 默认指标值
  default_metrics:
    attack_vector: 0.62
    attack_complexity: 0.44
    privileges_required: 0.62
    user_interaction: 0.85
    scope: 0.0
    confidentiality: 0.22
    integrity: 0.22
    availability: 0.22
  
  # 威胁类型默认指标映射
  threat_defaults:
    DDoS:
      attack_vector: 0.85
      attack_complexity: 0.77
      availability: 0.56
    DoS:
      attack_vector: 0.85
      attack_complexity: 0.77
      availability: 0.56
    Infiltration:
      attack_vector: 0.62
      attack_complexity: 0.44
      confidentiality: 0.56
    Malware:
      attack_vector: 0.62
      attack_complexity: 0.44
      integrity: 0.56
    "Port Scan":
      attack_vector: 0.85
      attack_complexity: 0.77
      confidentiality: 0.22
    "Web Attack":
      attack_vector: 0.85
      attack_complexity: 0.44
      integrity: 0.22

# 日志配置
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # 文件日志
  file_logging:
    enabled: true
    filename: "sagin_agents.log"
    max_size: "10MB"
    backup_count: 5
  
  # 控制台日志
  console_logging:
    enabled: true
    verbose: false

# 性能配置
performance:
  # 并发处理
  max_workers: 4
  batch_size: 10
  
  # 缓存配置
  cache:
    enabled: true
    max_size: 1000
    ttl: 3600  # 秒
  
  # 超时配置
  timeouts:
    llm_request: 30  # 秒
    file_processing: 60  # 秒
    agent_processing: 300  # 秒

# 安全配置
security:
  # 输入验证
  input_validation:
    max_file_size: "100MB"
    allowed_extensions: [".json", ".csv", ".txt"]
    sanitize_input: true
  
  # 输出安全
  output_security:
    sanitize_output: true
    max_output_size: "50MB"

# 工作流配置
workflow:
  # 协作工作流
  collaborative:
    enabled: true
    sequential_processing: true
    error_handling: "continue"  # continue, stop, retry
    
  # 独立运行
  independent:
    enabled: true
    parallel_processing: false
    
# 测试配置
testing:
  # 测试数据
  test_data_dir: "./data/test"
  
  # 测试模式配置
  test_mode:
    use_mock_llm: true
    generate_synthetic_data: true
    skip_external_calls: true
    
  # 验证配置
  validation:
    strict_mode: false
    validate_outputs: true
    check_data_integrity: true

# 开发配置
development:
  debug_mode: false
  profiling: false
  verbose_logging: false
  
  # 开发工具
  tools:
    auto_reload: false
    hot_reload: false
