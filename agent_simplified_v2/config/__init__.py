"""
配置模块
提供配置文件加载和管理功能
"""

import sys
from pathlib import Path
import yaml
import json
from typing import Dict, Any, Optional

# 添加core模块到路径
sys.path.append(str(Path(__file__).parent.parent))

from core.config_manager import Config, get_config, set_global_config


def load_config_from_file(config_path: str) -> Config:
    """
    从文件加载配置
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        Config对象
    """
    config_file = Path(config_path)
    
    if not config_file.exists():
        raise FileNotFoundError(f"配置文件不存在: {config_path}")
    
    # 根据文件扩展名选择加载方式
    if config_file.suffix.lower() == '.yaml' or config_file.suffix.lower() == '.yml':
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
    elif config_file.suffix.lower() == '.json':
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
    else:
        raise ValueError(f"不支持的配置文件格式: {config_file.suffix}")
    
    # 创建Config对象
    config = Config.from_dict(config_data)
    
    return config


def load_default_config() -> Config:
    """
    加载默认配置
    
    Returns:
        默认Config对象
    """
    default_config_path = Path(__file__).parent / "default_config.yaml"
    return load_config_from_file(str(default_config_path))


def setup_global_config(config_path: Optional[str] = None) -> Config:
    """
    设置全局配置
    
    Args:
        config_path: 配置文件路径，如果为None则使用默认配置
        
    Returns:
        Config对象
    """
    if config_path:
        config = load_config_from_file(config_path)
    else:
        config = load_default_config()
    
    # 设置为全局配置
    set_global_config(config)
    
    return config


# 导出主要函数和类
__all__ = [
    'load_config_from_file',
    'load_default_config', 
    'setup_global_config',
    'Config',
    'get_config'
]
